import React, { createContext, useContext, useReducer, useEffect } from 'react';
import productsData from '../data/products.json';

const ProductContext = createContext();

const productReducer = (state, action) => {
  switch (action.type) {
    case 'SET_PRODUCTS':
      return {
        ...state,
        products: action.payload
      };

    case 'ADD_PRODUCT':
      const newProduct = {
        ...action.payload,
        id: Math.max(...state.products.map(p => p.id)) + 1
      };
      const updatedProducts = [...state.products, newProduct];
      localStorage.setItem('ecommerce_products', JSON.stringify(updatedProducts));
      return {
        ...state,
        products: updatedProducts
      };

    case 'UPDATE_PRODUCT':
      const updated = state.products.map(product =>
        product.id === action.payload.id ? action.payload : product
      );
      localStorage.setItem('ecommerce_products', JSON.stringify(updated));
      return {
        ...state,
        products: updated
      };

    case 'DELETE_PRODUCT':
      const filtered = state.products.filter(product => product.id !== action.payload);
      localStorage.setItem('ecommerce_products', JSON.stringify(filtered));
      return {
        ...state,
        products: filtered
      };

    default:
      return state;
  }
};

const initialState = {
  products: [],
  loading: false,
  error: null
};

export const ProductProvider = ({ children }) => {
  const [state, dispatch] = useReducer(productReducer, initialState);

  useEffect(() => {
    // Load products from localStorage or use default data
    const savedProducts = localStorage.getItem('ecommerce_products');
    if (savedProducts) {
      dispatch({ type: 'SET_PRODUCTS', payload: JSON.parse(savedProducts) });
    } else {
      dispatch({ type: 'SET_PRODUCTS', payload: productsData });
      localStorage.setItem('ecommerce_products', JSON.stringify(productsData));
    }
  }, []);

  const addProduct = (productData) => {
    dispatch({ type: 'ADD_PRODUCT', payload: productData });
  };

  const updateProduct = (productData) => {
    dispatch({ type: 'UPDATE_PRODUCT', payload: productData });
  };

  const deleteProduct = (productId) => {
    dispatch({ type: 'DELETE_PRODUCT', payload: productId });
  };

  const getProductById = (id) => {
    return state.products.find(product => product.id === parseInt(id));
  };

  const getProductsByCategory = (category) => {
    if (category === 'all') return state.products;
    return state.products.filter(product => 
      product.category.toLowerCase() === category.toLowerCase()
    );
  };

  const value = {
    products: state.products,
    loading: state.loading,
    error: state.error,
    addProduct,
    updateProduct,
    deleteProduct,
    getProductById,
    getProductsByCategory
  };

  return (
    <ProductContext.Provider value={value}>
      {children}
    </ProductContext.Provider>
  );
};

export const useProducts = () => {
  const context = useContext(ProductContext);
  if (!context) {
    throw new Error('useProducts must be used within a ProductProvider');
  }
  return context;
};
