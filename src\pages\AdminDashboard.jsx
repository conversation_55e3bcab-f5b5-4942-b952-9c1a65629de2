import React, { useState } from 'react';
import {
  BarChart3,
  Users,
  Package,
  DollarSign,
  TrendingUp,
  ShoppingCart,
  Settings,
  LogOut,
  Menu,
  X,
  Eye,
  Edit,
  Trash2
} from 'lucide-react';
import ProductManagement from '../components/ProductManagement';
import CustomerManagement from '../components/CustomerManagement';

const AdminDashboard = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('dashboard');

  const stats = [
    { name: 'Total Revenue', value: '$45,231', change: '+20.1%', icon: DollarSign, color: 'text-green-600' },
    { name: 'Orders', value: '1,234', change: '+15.3%', icon: ShoppingCart, color: 'text-blue-600' },
    { name: 'Customers', value: '2,345', change: '+8.2%', icon: Users, color: 'text-purple-600' },
    { name: 'Products', value: '567', change: '+3.1%', icon: Package, color: 'text-orange-600' },
  ];

  const recentOrders = [
    { id: '#1234', customer: '<PERSON>', amount: '$129.99', status: 'Completed', date: '2024-01-15' },
    { id: '#1235', customer: 'Jane Smith', amount: '$89.50', status: 'Processing', date: '2024-01-15' },
    { id: '#1236', customer: 'Mike Johnson', amount: '$199.99', status: 'Shipped', date: '2024-01-14' },
    { id: '#1237', customer: 'Sarah Wilson', amount: '$79.99', status: 'Pending', date: '2024-01-14' },
  ];

  const topProducts = [
    { name: 'Premium Cotton T-Shirt', sales: 234, revenue: '$7,020' },
    { name: 'Elegant Summer Dress', sales: 189, revenue: '$15,111' },
    { name: 'Leather Crossbody Bag', sales: 156, revenue: '$23,400' },
    { name: 'Classic Denim Jacket', sales: 143, revenue: '$12,870' },
  ];

  const sidebarItems = [
    { name: 'Dashboard', icon: BarChart3, key: 'dashboard' },
    { name: 'Orders', icon: ShoppingCart, key: 'orders' },
    { name: 'Products', icon: Package, key: 'products' },
    { name: 'Customers', icon: Users, key: 'customers' },
    { name: 'Analytics', icon: TrendingUp, key: 'analytics' },
    { name: 'Settings', icon: Settings, key: 'settings' },
  ];

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      {/* Sidebar */}
      <div className={`fixed inset-y-0 left-0 z-50 w-64 bg-gray-800 transform ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'} transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0`}>
        <div className="flex items-center justify-between h-16 px-6 border-b border-gray-700">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-lg">YS</span>
            </div>
            <span className="text-xl font-bold">YourShop Admin</span>
          </div>
          <button
            onClick={() => setSidebarOpen(false)}
            className="lg:hidden text-gray-400 hover:text-white"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        <nav className="mt-6">
          <div className="px-6 space-y-1">
            {sidebarItems.map((item) => (
              <button
                key={item.name}
                onClick={() => setActiveTab(item.key)}
                className={`flex items-center w-full px-4 py-3 text-sm font-medium rounded-lg transition-colors ${
                  activeTab === item.key
                    ? 'bg-primary-600 text-white'
                    : 'text-gray-300 hover:bg-gray-700 hover:text-white'
                }`}
              >
                <item.icon className="w-5 h-5 mr-3" />
                {item.name}
              </button>
            ))}
          </div>
          
          <div className="mt-8 pt-6 border-t border-gray-700">
            <div className="px-6">
              <button className="flex items-center w-full px-4 py-3 text-sm font-medium text-gray-300 rounded-lg hover:bg-gray-700 hover:text-white transition-colors">
                <LogOut className="w-5 h-5 mr-3" />
                Sign Out
              </button>
            </div>
          </div>
        </nav>
      </div>

      {/* Main Content */}
      <div className="lg:pl-64">
        {/* Header */}
        <header className="bg-gray-800 border-b border-gray-700">
          <div className="flex items-center justify-between h-16 px-6">
            <div className="flex items-center">
              <button
                onClick={() => setSidebarOpen(true)}
                className="lg:hidden text-gray-400 hover:text-white mr-4"
              >
                <Menu className="w-6 h-6" />
              </button>
              <h1 className="text-2xl font-bold">Dashboard</h1>
            </div>
            <div className="flex items-center space-x-4">
              <div className="text-right">
                <p className="text-sm text-gray-300">Welcome back,</p>
                <p className="font-medium">Admin User</p>
              </div>
              <div className="w-10 h-10 bg-primary-500 rounded-full flex items-center justify-center">
                <span className="text-sm font-medium">AU</span>
              </div>
            </div>
          </div>
        </header>

        {/* Dashboard Content */}
        <main className="p-6">
          {activeTab === 'dashboard' && (
            <>
              {/* Stats Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                {stats.map((stat) => (
                  <div key={stat.name} className="bg-gray-800 rounded-xl p-6 border border-gray-700">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-gray-400 text-sm">{stat.name}</p>
                        <p className="text-2xl font-bold mt-1">{stat.value}</p>
                        <p className={`text-sm mt-1 ${stat.color}`}>{stat.change} from last month</p>
                      </div>
                      <div className={`p-3 rounded-lg bg-gray-700 ${stat.color}`}>
                        <stat.icon className="w-6 h-6" />
                      </div>
                    </div>
                  </div>
                ))}
              </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Recent Orders */}
            <div className="bg-gray-800 rounded-xl border border-gray-700">
              <div className="p-6 border-b border-gray-700">
                <h2 className="text-xl font-bold">Recent Orders</h2>
              </div>
              <div className="p-6">
                <div className="space-y-4">
                  {recentOrders.map((order) => (
                    <div key={order.id} className="flex items-center justify-between p-4 bg-gray-700 rounded-lg">
                      <div>
                        <p className="font-medium">{order.id}</p>
                        <p className="text-sm text-gray-400">{order.customer}</p>
                        <p className="text-xs text-gray-500">{order.date}</p>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">{order.amount}</p>
                        <span className={`inline-block px-2 py-1 text-xs rounded-full ${
                          order.status === 'Completed' ? 'bg-green-900 text-green-300' :
                          order.status === 'Processing' ? 'bg-yellow-900 text-yellow-300' :
                          order.status === 'Shipped' ? 'bg-blue-900 text-blue-300' :
                          'bg-gray-600 text-gray-300'
                        }`}>
                          {order.status}
                        </span>
                      </div>
                      <div className="flex space-x-2">
                        <button className="p-1 text-gray-400 hover:text-white">
                          <Eye className="w-4 h-4" />
                        </button>
                        <button className="p-1 text-gray-400 hover:text-white">
                          <Edit className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Top Products */}
            <div className="bg-gray-800 rounded-xl border border-gray-700">
              <div className="p-6 border-b border-gray-700">
                <h2 className="text-xl font-bold">Top Products</h2>
              </div>
              <div className="p-6">
                <div className="space-y-4">
                  {topProducts.map((product, index) => (
                    <div key={product.name} className="flex items-center justify-between p-4 bg-gray-700 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-primary-500 rounded-full flex items-center justify-center text-sm font-bold">
                          {index + 1}
                        </div>
                        <div>
                          <p className="font-medium">{product.name}</p>
                          <p className="text-sm text-gray-400">{product.sales} sales</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-medium text-green-400">{product.revenue}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* System Status */}
          <div className="mt-8 bg-gray-800 rounded-xl border border-gray-700">
            <div className="p-6 border-b border-gray-700">
              <h2 className="text-xl font-bold">System Status</h2>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="w-16 h-16 bg-green-900 rounded-full flex items-center justify-center mx-auto mb-3">
                    <div className="w-8 h-8 bg-green-500 rounded-full"></div>
                  </div>
                  <p className="font-medium">Server Status</p>
                  <p className="text-sm text-green-400">Online</p>
                </div>
                <div className="text-center">
                  <div className="w-16 h-16 bg-blue-900 rounded-full flex items-center justify-center mx-auto mb-3">
                    <div className="w-8 h-8 bg-blue-500 rounded-full"></div>
                  </div>
                  <p className="font-medium">Database</p>
                  <p className="text-sm text-blue-400">Connected</p>
                </div>
                <div className="text-center">
                  <div className="w-16 h-16 bg-yellow-900 rounded-full flex items-center justify-center mx-auto mb-3">
                    <div className="w-8 h-8 bg-yellow-500 rounded-full"></div>
                  </div>
                  <p className="font-medium">Payment Gateway</p>
                  <p className="text-sm text-yellow-400">Maintenance</p>
                </div>
              </div>
            </div>
          </div>
            </>
          )}

          {/* Products Tab */}
          {activeTab === 'products' && <ProductManagement />}

          {/* Other Tabs */}
          {activeTab === 'orders' && (
            <div className="bg-gray-800 rounded-xl p-6 border border-gray-700">
              <h2 className="text-2xl font-bold mb-4">Order Management</h2>
              <p className="text-gray-400">Order management functionality coming soon...</p>
            </div>
          )}

          {activeTab === 'customers' && <CustomerManagement />}

          {activeTab === 'analytics' && (
            <div className="bg-gray-800 rounded-xl p-6 border border-gray-700">
              <h2 className="text-2xl font-bold mb-4">Analytics</h2>
              <p className="text-gray-400">Analytics dashboard coming soon...</p>
            </div>
          )}

          {activeTab === 'settings' && (
            <div className="bg-gray-800 rounded-xl p-6 border border-gray-700">
              <h2 className="text-2xl font-bold mb-4">Settings</h2>
              <p className="text-gray-400">Settings panel coming soon...</p>
            </div>
          )}
        </main>
      </div>

      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}
    </div>
  );
};

export default AdminDashboard;
