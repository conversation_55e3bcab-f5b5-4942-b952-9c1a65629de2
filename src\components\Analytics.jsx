import React, { useState, useEffect } from 'react';
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  ShoppingCart,
  Users,
  Package,
  Calendar,
  BarChart3,
  PieChart,
  Activity,
  Target,
  Award
} from 'lucide-react';
import { useProducts } from '../context/ProductContext';
import { useCustomers } from '../context/CustomerContext';

const Analytics = () => {
  const { products } = useProducts();
  const { customers, getCustomerStats } = useCustomers();
  const [timeRange, setTimeRange] = useState('30d');
  const [analytics, setAnalytics] = useState({});

  useEffect(() => {
    calculateAnalytics();
  }, [products, customers, timeRange]);

  const calculateAnalytics = () => {
    const customerStats = getCustomerStats();
    
    // Calculate product analytics
    const totalProducts = products.length;
    const featuredProducts = products.filter(p => p.featured).length;
    const avgProductPrice = products.reduce((sum, p) => sum + p.price, 0) / totalProducts || 0;
    
    // Calculate category distribution
    const categoryStats = products.reduce((acc, product) => {
      acc[product.category] = (acc[product.category] || 0) + 1;
      return acc;
    }, {});

    // Calculate revenue trends (simulated data)
    const revenueData = generateRevenueData(timeRange);
    const orderData = generateOrderData(timeRange);
    
    // Top performing products (simulated)
    const topProducts = products
      .map(product => ({
        ...product,
        sales: Math.floor(Math.random() * 100) + 10,
        revenue: (Math.floor(Math.random() * 100) + 10) * product.price
      }))
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 5);

    // Customer insights
    const newCustomersThisMonth = Math.floor(customers.length * 0.3);
    const returningCustomers = customers.length - newCustomersThisMonth;
    const customerGrowth = 15.3; // Simulated growth percentage

    setAnalytics({
      revenue: {
        total: customerStats.totalRevenue,
        growth: 23.5,
        data: revenueData
      },
      orders: {
        total: customers.reduce((sum, c) => sum + c.totalOrders, 0),
        growth: 18.2,
        data: orderData
      },
      customers: {
        total: customerStats.total,
        new: newCustomersThisMonth,
        returning: returningCustomers,
        growth: customerGrowth,
        vip: customerStats.vip
      },
      products: {
        total: totalProducts,
        featured: featuredProducts,
        avgPrice: avgProductPrice,
        categories: categoryStats,
        topPerforming: topProducts
      },
      conversion: {
        rate: 3.2,
        visitors: 12450,
        sales: 398
      }
    });
  };

  const generateRevenueData = (range) => {
    const days = range === '7d' ? 7 : range === '30d' ? 30 : 90;
    return Array.from({ length: days }, (_, i) => ({
      date: new Date(Date.now() - (days - i - 1) * 24 * 60 * 60 * 1000).toLocaleDateString(),
      value: Math.floor(Math.random() * 1000) + 200
    }));
  };

  const generateOrderData = (range) => {
    const days = range === '7d' ? 7 : range === '30d' ? 30 : 90;
    return Array.from({ length: days }, (_, i) => ({
      date: new Date(Date.now() - (days - i - 1) * 24 * 60 * 60 * 1000).toLocaleDateString(),
      value: Math.floor(Math.random() * 50) + 5
    }));
  };

  const StatCard = ({ title, value, growth, icon: Icon, color = 'blue' }) => (
    <div className="bg-gray-800 rounded-xl p-6 border border-gray-700">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-gray-400 text-sm">{title}</p>
          <p className="text-2xl font-bold mt-1">{value}</p>
          {growth !== undefined && (
            <div className="flex items-center mt-2">
              {growth >= 0 ? (
                <TrendingUp className="w-4 h-4 text-green-400 mr-1" />
              ) : (
                <TrendingDown className="w-4 h-4 text-red-400 mr-1" />
              )}
              <span className={`text-sm ${growth >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                {Math.abs(growth)}%
              </span>
              <span className="text-gray-400 text-sm ml-1">vs last month</span>
            </div>
          )}
        </div>
        <div className={`p-3 rounded-lg bg-${color}-900 text-${color}-300`}>
          <Icon className="w-6 h-6" />
        </div>
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Analytics Dashboard</h2>
          <p className="text-gray-400 mt-1">Track your business performance</p>
        </div>
        
        {/* Time Range Selector */}
        <div className="flex bg-gray-700 rounded-lg p-1">
          {['7d', '30d', '90d'].map((range) => (
            <button
              key={range}
              onClick={() => setTimeRange(range)}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                timeRange === range
                  ? 'bg-primary-600 text-white'
                  : 'text-gray-300 hover:text-white'
              }`}
            >
              {range === '7d' ? '7 Days' : range === '30d' ? '30 Days' : '90 Days'}
            </button>
          ))}
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Total Revenue"
          value={`$${analytics.revenue?.total?.toFixed(2) || '0.00'}`}
          growth={analytics.revenue?.growth}
          icon={DollarSign}
          color="green"
        />
        <StatCard
          title="Total Orders"
          value={analytics.orders?.total || 0}
          growth={analytics.orders?.growth}
          icon={ShoppingCart}
          color="blue"
        />
        <StatCard
          title="Total Customers"
          value={analytics.customers?.total || 0}
          growth={analytics.customers?.growth}
          icon={Users}
          color="purple"
        />
        <StatCard
          title="Products"
          value={analytics.products?.total || 0}
          icon={Package}
          color="orange"
        />
      </div>

      {/* Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Revenue Chart */}
        <div className="bg-gray-800 rounded-xl p-6 border border-gray-700">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold">Revenue Trend</h3>
            <BarChart3 className="w-5 h-5 text-gray-400" />
          </div>
          <div className="h-64 flex items-end justify-between space-x-1">
            {analytics.revenue?.data?.slice(-7).map((item, index) => (
              <div key={index} className="flex flex-col items-center flex-1">
                <div
                  className="bg-primary-500 rounded-t w-full"
                  style={{
                    height: `${(item.value / Math.max(...analytics.revenue.data.map(d => d.value))) * 200}px`
                  }}
                />
                <span className="text-xs text-gray-400 mt-2 transform -rotate-45">
                  {item.date.split('/').slice(0, 2).join('/')}
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* Category Distribution */}
        <div className="bg-gray-800 rounded-xl p-6 border border-gray-700">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold">Product Categories</h3>
            <PieChart className="w-5 h-5 text-gray-400" />
          </div>
          <div className="space-y-4">
            {Object.entries(analytics.products?.categories || {}).map(([category, count], index) => {
              const colors = ['bg-blue-500', 'bg-green-500', 'bg-purple-500', 'bg-orange-500'];
              const percentage = ((count / analytics.products?.total) * 100).toFixed(1);
              
              return (
                <div key={category} className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className={`w-3 h-3 rounded-full ${colors[index % colors.length]} mr-3`} />
                    <span className="text-gray-300">{category}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-gray-400">{count}</span>
                    <span className="text-sm text-gray-500">({percentage}%)</span>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Additional Metrics */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Customer Insights */}
        <div className="bg-gray-800 rounded-xl p-6 border border-gray-700">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold">Customer Insights</h3>
            <Users className="w-5 h-5 text-gray-400" />
          </div>
          <div className="space-y-4">
            <div className="flex justify-between">
              <span className="text-gray-400">New Customers</span>
              <span className="text-green-400">{analytics.customers?.new || 0}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Returning Customers</span>
              <span className="text-blue-400">{analytics.customers?.returning || 0}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">VIP Customers</span>
              <span className="text-purple-400">{analytics.customers?.vip || 0}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Avg Order Value</span>
              <span className="text-orange-400">${(analytics.revenue?.total / analytics.orders?.total || 0).toFixed(2)}</span>
            </div>
          </div>
        </div>

        {/* Conversion Metrics */}
        <div className="bg-gray-800 rounded-xl p-6 border border-gray-700">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold">Conversion</h3>
            <Target className="w-5 h-5 text-gray-400" />
          </div>
          <div className="space-y-4">
            <div className="text-center">
              <div className="text-3xl font-bold text-primary-400">
                {analytics.conversion?.rate || 0}%
              </div>
              <div className="text-gray-400 text-sm">Conversion Rate</div>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Visitors</span>
              <span className="text-blue-400">{analytics.conversion?.visitors?.toLocaleString() || 0}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">Sales</span>
              <span className="text-green-400">{analytics.conversion?.sales || 0}</span>
            </div>
          </div>
        </div>

        {/* Top Products */}
        <div className="bg-gray-800 rounded-xl p-6 border border-gray-700">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold">Top Products</h3>
            <Award className="w-5 h-5 text-gray-400" />
          </div>
          <div className="space-y-3">
            {analytics.products?.topPerforming?.slice(0, 5).map((product, index) => (
              <div key={product.id} className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="w-8 h-8 bg-primary-500 rounded-full flex items-center justify-center text-xs font-bold mr-3">
                    {index + 1}
                  </div>
                  <div>
                    <div className="text-sm font-medium text-white truncate max-w-32">
                      {product.name}
                    </div>
                    <div className="text-xs text-gray-400">{product.sales} sales</div>
                  </div>
                </div>
                <div className="text-sm font-medium text-green-400">
                  ${product.revenue?.toFixed(0)}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Performance Summary */}
      <div className="bg-gray-800 rounded-xl p-6 border border-gray-700">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold">Performance Summary</h3>
          <Activity className="w-5 h-5 text-gray-400" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="text-2xl font-bold text-green-400 mb-2">
              ${analytics.products?.avgPrice?.toFixed(2) || '0.00'}
            </div>
            <div className="text-gray-400 text-sm">Average Product Price</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-400 mb-2">
              {analytics.products?.featured || 0}
            </div>
            <div className="text-gray-400 text-sm">Featured Products</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-400 mb-2">
              {((analytics.customers?.vip / analytics.customers?.total) * 100 || 0).toFixed(1)}%
            </div>
            <div className="text-gray-400 text-sm">VIP Customer Rate</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Analytics;
