import React, { createContext, useContext, useReducer, useEffect } from 'react';

const CustomerContext = createContext();

// Sample customer data
const initialCustomers = [
  {
    id: 1,
    firstName: '<PERSON>',
    lastName: 'Doe',
    email: '<EMAIL>',
    phone: '+****************',
    address: '123 Main St, New York, NY 10001',
    dateJoined: '2024-01-15',
    totalOrders: 12,
    totalSpent: 1250.99,
    status: 'Active',
    lastOrder: '2024-06-10',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face'
  },
  {
    id: 2,
    firstName: 'Jane',
    lastName: 'Smith',
    email: '<EMAIL>',
    phone: '+****************',
    address: '456 Oak Ave, Los Angeles, CA 90210',
    dateJoined: '2024-02-20',
    totalOrders: 8,
    totalSpent: 890.50,
    status: 'Active',
    lastOrder: '2024-06-12',
    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face'
  },
  {
    id: 3,
    firstName: 'Mike',
    lastName: 'Johnson',
    email: '<EMAIL>',
    phone: '+****************',
    address: '789 Pine St, Chicago, IL 60601',
    dateJoined: '2024-03-10',
    totalOrders: 5,
    totalSpent: 425.75,
    status: 'Active',
    lastOrder: '2024-06-08',
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face'
  },
  {
    id: 4,
    firstName: 'Sarah',
    lastName: 'Wilson',
    email: '<EMAIL>',
    phone: '+****************',
    address: '321 Elm St, Miami, FL 33101',
    dateJoined: '2024-01-05',
    totalOrders: 15,
    totalSpent: 1850.25,
    status: 'VIP',
    lastOrder: '2024-06-13',
    avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face'
  },
  {
    id: 5,
    firstName: 'David',
    lastName: 'Brown',
    email: '<EMAIL>',
    phone: '+****************',
    address: '654 Maple Dr, Seattle, WA 98101',
    dateJoined: '2024-04-15',
    totalOrders: 3,
    totalSpent: 180.00,
    status: 'Inactive',
    lastOrder: '2024-05-20',
    avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=100&h=100&fit=crop&crop=face'
  }
];

const customerReducer = (state, action) => {
  switch (action.type) {
    case 'SET_CUSTOMERS':
      return {
        ...state,
        customers: action.payload
      };

    case 'ADD_CUSTOMER':
      const newCustomer = {
        ...action.payload,
        id: Math.max(...state.customers.map(c => c.id)) + 1,
        dateJoined: new Date().toISOString().split('T')[0],
        totalOrders: 0,
        totalSpent: 0,
        status: 'Active',
        lastOrder: null
      };
      const updatedCustomers = [...state.customers, newCustomer];
      localStorage.setItem('ecommerce_customers', JSON.stringify(updatedCustomers));
      return {
        ...state,
        customers: updatedCustomers
      };

    case 'UPDATE_CUSTOMER':
      const updated = state.customers.map(customer =>
        customer.id === action.payload.id ? action.payload : customer
      );
      localStorage.setItem('ecommerce_customers', JSON.stringify(updated));
      return {
        ...state,
        customers: updated
      };

    case 'DELETE_CUSTOMER':
      const filtered = state.customers.filter(customer => customer.id !== action.payload);
      localStorage.setItem('ecommerce_customers', JSON.stringify(filtered));
      return {
        ...state,
        customers: filtered
      };

    case 'UPDATE_CUSTOMER_STATS':
      const statsUpdated = state.customers.map(customer =>
        customer.id === action.payload.customerId 
          ? {
              ...customer,
              totalOrders: customer.totalOrders + 1,
              totalSpent: customer.totalSpent + action.payload.orderAmount,
              lastOrder: new Date().toISOString().split('T')[0],
              status: customer.totalSpent + action.payload.orderAmount > 1000 ? 'VIP' : 'Active'
            }
          : customer
      );
      localStorage.setItem('ecommerce_customers', JSON.stringify(statsUpdated));
      return {
        ...state,
        customers: statsUpdated
      };

    default:
      return state;
  }
};

const initialState = {
  customers: [],
  loading: false,
  error: null
};

export const CustomerProvider = ({ children }) => {
  const [state, dispatch] = useReducer(customerReducer, initialState);

  useEffect(() => {
    // Load customers from localStorage or use default data
    const savedCustomers = localStorage.getItem('ecommerce_customers');
    if (savedCustomers) {
      dispatch({ type: 'SET_CUSTOMERS', payload: JSON.parse(savedCustomers) });
    } else {
      dispatch({ type: 'SET_CUSTOMERS', payload: initialCustomers });
      localStorage.setItem('ecommerce_customers', JSON.stringify(initialCustomers));
    }
  }, []);

  const addCustomer = (customerData) => {
    dispatch({ type: 'ADD_CUSTOMER', payload: customerData });
  };

  const updateCustomer = (customerData) => {
    dispatch({ type: 'UPDATE_CUSTOMER', payload: customerData });
  };

  const deleteCustomer = (customerId) => {
    dispatch({ type: 'DELETE_CUSTOMER', payload: customerId });
  };

  const updateCustomerStats = (customerId, orderAmount) => {
    dispatch({ type: 'UPDATE_CUSTOMER_STATS', payload: { customerId, orderAmount } });
  };

  const getCustomerById = (id) => {
    return state.customers.find(customer => customer.id === parseInt(id));
  };

  const getCustomersByStatus = (status) => {
    if (status === 'all') return state.customers;
    return state.customers.filter(customer => customer.status.toLowerCase() === status.toLowerCase());
  };

  const getCustomerStats = () => {
    const total = state.customers.length;
    const active = state.customers.filter(c => c.status === 'Active').length;
    const vip = state.customers.filter(c => c.status === 'VIP').length;
    const inactive = state.customers.filter(c => c.status === 'Inactive').length;
    const totalRevenue = state.customers.reduce((sum, c) => sum + c.totalSpent, 0);
    const avgOrderValue = totalRevenue / state.customers.reduce((sum, c) => sum + c.totalOrders, 0) || 0;

    return {
      total,
      active,
      vip,
      inactive,
      totalRevenue,
      avgOrderValue
    };
  };

  const value = {
    customers: state.customers,
    loading: state.loading,
    error: state.error,
    addCustomer,
    updateCustomer,
    deleteCustomer,
    updateCustomerStats,
    getCustomerById,
    getCustomersByStatus,
    getCustomerStats
  };

  return (
    <CustomerContext.Provider value={value}>
      {children}
    </CustomerContext.Provider>
  );
};

export const useCustomers = () => {
  const context = useContext(CustomerContext);
  if (!context) {
    throw new Error('useCustomers must be used within a CustomerProvider');
  }
  return context;
};
