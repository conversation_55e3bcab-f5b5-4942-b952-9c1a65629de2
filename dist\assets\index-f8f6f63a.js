function Jd(e,t){for(var r=0;r<t.length;r++){const n=t[r];if(typeof n!="string"&&!Array.isArray(n)){for(const l in n)if(l!=="default"&&!(l in e)){const a=Object.getOwnPropertyDescriptor(n,l);a&&Object.defineProperty(e,l,a.get?a:{enumerable:!0,get:()=>n[l]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const l of document.querySelectorAll('link[rel="modulepreload"]'))n(l);new MutationObserver(l=>{for(const a of l)if(a.type==="childList")for(const i of a.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&n(i)}).observe(document,{childList:!0,subtree:!0});function r(l){const a={};return l.integrity&&(a.integrity=l.integrity),l.referrerPolicy&&(a.referrerPolicy=l.referrerPolicy),l.crossOrigin==="use-credentials"?a.credentials="include":l.crossOrigin==="anonymous"?a.credentials="omit":a.credentials="same-origin",a}function n(l){if(l.ep)return;l.ep=!0;const a=r(l);fetch(l.href,a)}})();function Gd(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var ac={exports:{}},As={},ic={exports:{}},A={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var vn=Symbol.for("react.element"),Kd=Symbol.for("react.portal"),qd=Symbol.for("react.fragment"),Xd=Symbol.for("react.strict_mode"),Zd=Symbol.for("react.profiler"),em=Symbol.for("react.provider"),tm=Symbol.for("react.context"),rm=Symbol.for("react.forward_ref"),nm=Symbol.for("react.suspense"),sm=Symbol.for("react.memo"),lm=Symbol.for("react.lazy"),Ai=Symbol.iterator;function am(e){return e===null||typeof e!="object"?null:(e=Ai&&e[Ai]||e["@@iterator"],typeof e=="function"?e:null)}var oc={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},cc=Object.assign,uc={};function Cr(e,t,r){this.props=e,this.context=t,this.refs=uc,this.updater=r||oc}Cr.prototype.isReactComponent={};Cr.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Cr.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function dc(){}dc.prototype=Cr.prototype;function Ma(e,t,r){this.props=e,this.context=t,this.refs=uc,this.updater=r||oc}var za=Ma.prototype=new dc;za.constructor=Ma;cc(za,Cr.prototype);za.isPureReactComponent=!0;var Ii=Array.isArray,mc=Object.prototype.hasOwnProperty,Aa={current:null},fc={key:!0,ref:!0,__self:!0,__source:!0};function pc(e,t,r){var n,l={},a=null,i=null;if(t!=null)for(n in t.ref!==void 0&&(i=t.ref),t.key!==void 0&&(a=""+t.key),t)mc.call(t,n)&&!fc.hasOwnProperty(n)&&(l[n]=t[n]);var o=arguments.length-2;if(o===1)l.children=r;else if(1<o){for(var c=Array(o),u=0;u<o;u++)c[u]=arguments[u+2];l.children=c}if(e&&e.defaultProps)for(n in o=e.defaultProps,o)l[n]===void 0&&(l[n]=o[n]);return{$$typeof:vn,type:e,key:a,ref:i,props:l,_owner:Aa.current}}function im(e,t){return{$$typeof:vn,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function Ia(e){return typeof e=="object"&&e!==null&&e.$$typeof===vn}function om(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(r){return t[r]})}var Di=/\/+/g;function sl(e,t){return typeof e=="object"&&e!==null&&e.key!=null?om(""+e.key):t.toString(36)}function Yn(e,t,r,n,l){var a=typeof e;(a==="undefined"||a==="boolean")&&(e=null);var i=!1;if(e===null)i=!0;else switch(a){case"string":case"number":i=!0;break;case"object":switch(e.$$typeof){case vn:case Kd:i=!0}}if(i)return i=e,l=l(i),e=n===""?"."+sl(i,0):n,Ii(l)?(r="",e!=null&&(r=e.replace(Di,"$&/")+"/"),Yn(l,t,r,"",function(u){return u})):l!=null&&(Ia(l)&&(l=im(l,r+(!l.key||i&&i.key===l.key?"":(""+l.key).replace(Di,"$&/")+"/")+e)),t.push(l)),1;if(i=0,n=n===""?".":n+":",Ii(e))for(var o=0;o<e.length;o++){a=e[o];var c=n+sl(a,o);i+=Yn(a,t,r,c,l)}else if(c=am(e),typeof c=="function")for(e=c.call(e),o=0;!(a=e.next()).done;)a=a.value,c=n+sl(a,o++),i+=Yn(a,t,r,c,l);else if(a==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return i}function Tn(e,t,r){if(e==null)return e;var n=[],l=0;return Yn(e,n,"","",function(a){return t.call(r,a,l++)}),n}function cm(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(r){(e._status===0||e._status===-1)&&(e._status=1,e._result=r)},function(r){(e._status===0||e._status===-1)&&(e._status=2,e._result=r)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var pe={current:null},Jn={transition:null},um={ReactCurrentDispatcher:pe,ReactCurrentBatchConfig:Jn,ReactCurrentOwner:Aa};function hc(){throw Error("act(...) is not supported in production builds of React.")}A.Children={map:Tn,forEach:function(e,t,r){Tn(e,function(){t.apply(this,arguments)},r)},count:function(e){var t=0;return Tn(e,function(){t++}),t},toArray:function(e){return Tn(e,function(t){return t})||[]},only:function(e){if(!Ia(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};A.Component=Cr;A.Fragment=qd;A.Profiler=Zd;A.PureComponent=Ma;A.StrictMode=Xd;A.Suspense=nm;A.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=um;A.act=hc;A.cloneElement=function(e,t,r){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var n=cc({},e.props),l=e.key,a=e.ref,i=e._owner;if(t!=null){if(t.ref!==void 0&&(a=t.ref,i=Aa.current),t.key!==void 0&&(l=""+t.key),e.type&&e.type.defaultProps)var o=e.type.defaultProps;for(c in t)mc.call(t,c)&&!fc.hasOwnProperty(c)&&(n[c]=t[c]===void 0&&o!==void 0?o[c]:t[c])}var c=arguments.length-2;if(c===1)n.children=r;else if(1<c){o=Array(c);for(var u=0;u<c;u++)o[u]=arguments[u+2];n.children=o}return{$$typeof:vn,type:e.type,key:l,ref:a,props:n,_owner:i}};A.createContext=function(e){return e={$$typeof:tm,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:em,_context:e},e.Consumer=e};A.createElement=pc;A.createFactory=function(e){var t=pc.bind(null,e);return t.type=e,t};A.createRef=function(){return{current:null}};A.forwardRef=function(e){return{$$typeof:rm,render:e}};A.isValidElement=Ia;A.lazy=function(e){return{$$typeof:lm,_payload:{_status:-1,_result:e},_init:cm}};A.memo=function(e,t){return{$$typeof:sm,type:e,compare:t===void 0?null:t}};A.startTransition=function(e){var t=Jn.transition;Jn.transition={};try{e()}finally{Jn.transition=t}};A.unstable_act=hc;A.useCallback=function(e,t){return pe.current.useCallback(e,t)};A.useContext=function(e){return pe.current.useContext(e)};A.useDebugValue=function(){};A.useDeferredValue=function(e){return pe.current.useDeferredValue(e)};A.useEffect=function(e,t){return pe.current.useEffect(e,t)};A.useId=function(){return pe.current.useId()};A.useImperativeHandle=function(e,t,r){return pe.current.useImperativeHandle(e,t,r)};A.useInsertionEffect=function(e,t){return pe.current.useInsertionEffect(e,t)};A.useLayoutEffect=function(e,t){return pe.current.useLayoutEffect(e,t)};A.useMemo=function(e,t){return pe.current.useMemo(e,t)};A.useReducer=function(e,t,r){return pe.current.useReducer(e,t,r)};A.useRef=function(e){return pe.current.useRef(e)};A.useState=function(e){return pe.current.useState(e)};A.useSyncExternalStore=function(e,t,r){return pe.current.useSyncExternalStore(e,t,r)};A.useTransition=function(){return pe.current.useTransition()};A.version="18.3.1";ic.exports=A;var N=ic.exports;const xc=Gd(N),dm=Jd({__proto__:null,default:xc},[N]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var mm=N,fm=Symbol.for("react.element"),pm=Symbol.for("react.fragment"),hm=Object.prototype.hasOwnProperty,xm=mm.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,gm={key:!0,ref:!0,__self:!0,__source:!0};function gc(e,t,r){var n,l={},a=null,i=null;r!==void 0&&(a=""+r),t.key!==void 0&&(a=""+t.key),t.ref!==void 0&&(i=t.ref);for(n in t)hm.call(t,n)&&!gm.hasOwnProperty(n)&&(l[n]=t[n]);if(e&&e.defaultProps)for(n in t=e.defaultProps,t)l[n]===void 0&&(l[n]=t[n]);return{$$typeof:fm,type:e,key:a,ref:i,props:l,_owner:xm.current}}As.Fragment=pm;As.jsx=gc;As.jsxs=gc;ac.exports=As;var s=ac.exports,Ml={},yc={exports:{}},Pe={},vc={exports:{}},jc={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(b,L){var z=b.length;b.push(L);e:for(;0<z;){var V=z-1>>>1,O=b[V];if(0<l(O,L))b[V]=L,b[z]=O,z=V;else break e}}function r(b){return b.length===0?null:b[0]}function n(b){if(b.length===0)return null;var L=b[0],z=b.pop();if(z!==L){b[0]=z;e:for(var V=0,O=b.length,G=O>>>1;V<G;){var H=2*(V+1)-1,xe=b[H],ge=H+1,Ke=b[ge];if(0>l(xe,z))ge<O&&0>l(Ke,xe)?(b[V]=Ke,b[ge]=z,V=ge):(b[V]=xe,b[H]=z,V=H);else if(ge<O&&0>l(Ke,z))b[V]=Ke,b[ge]=z,V=ge;else break e}}return L}function l(b,L){var z=b.sortIndex-L.sortIndex;return z!==0?z:b.id-L.id}if(typeof performance=="object"&&typeof performance.now=="function"){var a=performance;e.unstable_now=function(){return a.now()}}else{var i=Date,o=i.now();e.unstable_now=function(){return i.now()-o}}var c=[],u=[],f=1,m=null,h=3,g=!1,j=!1,y=!1,w=typeof setTimeout=="function"?setTimeout:null,p=typeof clearTimeout=="function"?clearTimeout:null,d=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function x(b){for(var L=r(u);L!==null;){if(L.callback===null)n(u);else if(L.startTime<=b)n(u),L.sortIndex=L.expirationTime,t(c,L);else break;L=r(u)}}function v(b){if(y=!1,x(b),!j)if(r(c)!==null)j=!0,Gt(k);else{var L=r(u);L!==null&&Kt(v,L.startTime-b)}}function k(b,L){j=!1,y&&(y=!1,p(T),T=-1),g=!0;var z=h;try{for(x(L),m=r(c);m!==null&&(!(m.expirationTime>L)||b&&!_());){var V=m.callback;if(typeof V=="function"){m.callback=null,h=m.priorityLevel;var O=V(m.expirationTime<=L);L=e.unstable_now(),typeof O=="function"?m.callback=O:m===r(c)&&n(c),x(L)}else n(c);m=r(c)}if(m!==null)var G=!0;else{var H=r(u);H!==null&&Kt(v,H.startTime-L),G=!1}return G}finally{m=null,h=z,g=!1}}var C=!1,E=null,T=-1,F=5,M=-1;function _(){return!(e.unstable_now()-M<F)}function Ve(){if(E!==null){var b=e.unstable_now();M=b;var L=!0;try{L=E(!0,b)}finally{L?We():(C=!1,E=null)}}else C=!1}var We;if(typeof d=="function")We=function(){d(Ve)};else if(typeof MessageChannel<"u"){var Rt=new MessageChannel,_n=Rt.port2;Rt.port1.onmessage=Ve,We=function(){_n.postMessage(null)}}else We=function(){w(Ve,0)};function Gt(b){E=b,C||(C=!0,We())}function Kt(b,L){T=w(function(){b(e.unstable_now())},L)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(b){b.callback=null},e.unstable_continueExecution=function(){j||g||(j=!0,Gt(k))},e.unstable_forceFrameRate=function(b){0>b||125<b?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):F=0<b?Math.floor(1e3/b):5},e.unstable_getCurrentPriorityLevel=function(){return h},e.unstable_getFirstCallbackNode=function(){return r(c)},e.unstable_next=function(b){switch(h){case 1:case 2:case 3:var L=3;break;default:L=h}var z=h;h=L;try{return b()}finally{h=z}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(b,L){switch(b){case 1:case 2:case 3:case 4:case 5:break;default:b=3}var z=h;h=b;try{return L()}finally{h=z}},e.unstable_scheduleCallback=function(b,L,z){var V=e.unstable_now();switch(typeof z=="object"&&z!==null?(z=z.delay,z=typeof z=="number"&&0<z?V+z:V):z=V,b){case 1:var O=-1;break;case 2:O=250;break;case 5:O=**********;break;case 4:O=1e4;break;default:O=5e3}return O=z+O,b={id:f++,callback:L,priorityLevel:b,startTime:z,expirationTime:O,sortIndex:-1},z>V?(b.sortIndex=z,t(u,b),r(c)===null&&b===r(u)&&(y?(p(T),T=-1):y=!0,Kt(v,z-V))):(b.sortIndex=O,t(c,b),j||g||(j=!0,Gt(k))),b},e.unstable_shouldYield=_,e.unstable_wrapCallback=function(b){var L=h;return function(){var z=h;h=L;try{return b.apply(this,arguments)}finally{h=z}}}})(jc);vc.exports=jc;var ym=vc.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var vm=N,Ee=ym;function S(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,r=1;r<arguments.length;r++)t+="&args[]="+encodeURIComponent(arguments[r]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Nc=new Set,Gr={};function Ht(e,t){gr(e,t),gr(e+"Capture",t)}function gr(e,t){for(Gr[e]=t,e=0;e<t.length;e++)Nc.add(t[e])}var rt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),zl=Object.prototype.hasOwnProperty,jm=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Fi={},$i={};function Nm(e){return zl.call($i,e)?!0:zl.call(Fi,e)?!1:jm.test(e)?$i[e]=!0:(Fi[e]=!0,!1)}function wm(e,t,r,n){if(r!==null&&r.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return n?!1:r!==null?!r.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function Sm(e,t,r,n){if(t===null||typeof t>"u"||wm(e,t,r,n))return!0;if(n)return!1;if(r!==null)switch(r.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function he(e,t,r,n,l,a,i){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=n,this.attributeNamespace=l,this.mustUseProperty=r,this.propertyName=e,this.type=t,this.sanitizeURL=a,this.removeEmptyString=i}var ie={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){ie[e]=new he(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];ie[t]=new he(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){ie[e]=new he(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){ie[e]=new he(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){ie[e]=new he(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){ie[e]=new he(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){ie[e]=new he(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){ie[e]=new he(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){ie[e]=new he(e,5,!1,e.toLowerCase(),null,!1,!1)});var Da=/[\-:]([a-z])/g;function Fa(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Da,Fa);ie[t]=new he(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Da,Fa);ie[t]=new he(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Da,Fa);ie[t]=new he(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){ie[e]=new he(e,1,!1,e.toLowerCase(),null,!1,!1)});ie.xlinkHref=new he("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){ie[e]=new he(e,1,!1,e.toLowerCase(),null,!0,!0)});function $a(e,t,r,n){var l=ie.hasOwnProperty(t)?ie[t]:null;(l!==null?l.type!==0:n||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(Sm(t,r,l,n)&&(r=null),n||l===null?Nm(t)&&(r===null?e.removeAttribute(t):e.setAttribute(t,""+r)):l.mustUseProperty?e[l.propertyName]=r===null?l.type===3?!1:"":r:(t=l.attributeName,n=l.attributeNamespace,r===null?e.removeAttribute(t):(l=l.type,r=l===3||l===4&&r===!0?"":""+r,n?e.setAttributeNS(n,t,r):e.setAttribute(t,r))))}var at=vm.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Rn=Symbol.for("react.element"),Zt=Symbol.for("react.portal"),er=Symbol.for("react.fragment"),Ua=Symbol.for("react.strict_mode"),Al=Symbol.for("react.profiler"),wc=Symbol.for("react.provider"),Sc=Symbol.for("react.context"),Ba=Symbol.for("react.forward_ref"),Il=Symbol.for("react.suspense"),Dl=Symbol.for("react.suspense_list"),Va=Symbol.for("react.memo"),ct=Symbol.for("react.lazy"),kc=Symbol.for("react.offscreen"),Ui=Symbol.iterator;function Pr(e){return e===null||typeof e!="object"?null:(e=Ui&&e[Ui]||e["@@iterator"],typeof e=="function"?e:null)}var J=Object.assign,ll;function Ar(e){if(ll===void 0)try{throw Error()}catch(r){var t=r.stack.trim().match(/\n( *(at )?)/);ll=t&&t[1]||""}return`
`+ll+e}var al=!1;function il(e,t){if(!e||al)return"";al=!0;var r=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var n=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){n=u}e.call(t.prototype)}else{try{throw Error()}catch(u){n=u}e()}}catch(u){if(u&&n&&typeof u.stack=="string"){for(var l=u.stack.split(`
`),a=n.stack.split(`
`),i=l.length-1,o=a.length-1;1<=i&&0<=o&&l[i]!==a[o];)o--;for(;1<=i&&0<=o;i--,o--)if(l[i]!==a[o]){if(i!==1||o!==1)do if(i--,o--,0>o||l[i]!==a[o]){var c=`
`+l[i].replace(" at new "," at ");return e.displayName&&c.includes("<anonymous>")&&(c=c.replace("<anonymous>",e.displayName)),c}while(1<=i&&0<=o);break}}}finally{al=!1,Error.prepareStackTrace=r}return(e=e?e.displayName||e.name:"")?Ar(e):""}function km(e){switch(e.tag){case 5:return Ar(e.type);case 16:return Ar("Lazy");case 13:return Ar("Suspense");case 19:return Ar("SuspenseList");case 0:case 2:case 15:return e=il(e.type,!1),e;case 11:return e=il(e.type.render,!1),e;case 1:return e=il(e.type,!0),e;default:return""}}function Fl(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case er:return"Fragment";case Zt:return"Portal";case Al:return"Profiler";case Ua:return"StrictMode";case Il:return"Suspense";case Dl:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Sc:return(e.displayName||"Context")+".Consumer";case wc:return(e._context.displayName||"Context")+".Provider";case Ba:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Va:return t=e.displayName||null,t!==null?t:Fl(e.type)||"Memo";case ct:t=e._payload,e=e._init;try{return Fl(e(t))}catch{}}return null}function Cm(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return Fl(t);case 8:return t===Ua?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Ct(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Cc(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function bm(e){var t=Cc(e)?"checked":"value",r=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),n=""+e[t];if(!e.hasOwnProperty(t)&&typeof r<"u"&&typeof r.get=="function"&&typeof r.set=="function"){var l=r.get,a=r.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return l.call(this)},set:function(i){n=""+i,a.call(this,i)}}),Object.defineProperty(e,t,{enumerable:r.enumerable}),{getValue:function(){return n},setValue:function(i){n=""+i},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function On(e){e._valueTracker||(e._valueTracker=bm(e))}function bc(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var r=t.getValue(),n="";return e&&(n=Cc(e)?e.checked?"true":"false":e.value),e=n,e!==r?(t.setValue(e),!0):!1}function ls(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function $l(e,t){var r=t.checked;return J({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:r??e._wrapperState.initialChecked})}function Bi(e,t){var r=t.defaultValue==null?"":t.defaultValue,n=t.checked!=null?t.checked:t.defaultChecked;r=Ct(t.value!=null?t.value:r),e._wrapperState={initialChecked:n,initialValue:r,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Ec(e,t){t=t.checked,t!=null&&$a(e,"checked",t,!1)}function Ul(e,t){Ec(e,t);var r=Ct(t.value),n=t.type;if(r!=null)n==="number"?(r===0&&e.value===""||e.value!=r)&&(e.value=""+r):e.value!==""+r&&(e.value=""+r);else if(n==="submit"||n==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Bl(e,t.type,r):t.hasOwnProperty("defaultValue")&&Bl(e,t.type,Ct(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Vi(e,t,r){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var n=t.type;if(!(n!=="submit"&&n!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,r||t===e.value||(e.value=t),e.defaultValue=t}r=e.name,r!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,r!==""&&(e.name=r)}function Bl(e,t,r){(t!=="number"||ls(e.ownerDocument)!==e)&&(r==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+r&&(e.defaultValue=""+r))}var Ir=Array.isArray;function dr(e,t,r,n){if(e=e.options,t){t={};for(var l=0;l<r.length;l++)t["$"+r[l]]=!0;for(r=0;r<e.length;r++)l=t.hasOwnProperty("$"+e[r].value),e[r].selected!==l&&(e[r].selected=l),l&&n&&(e[r].defaultSelected=!0)}else{for(r=""+Ct(r),t=null,l=0;l<e.length;l++){if(e[l].value===r){e[l].selected=!0,n&&(e[l].defaultSelected=!0);return}t!==null||e[l].disabled||(t=e[l])}t!==null&&(t.selected=!0)}}function Vl(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(S(91));return J({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Wi(e,t){var r=t.value;if(r==null){if(r=t.children,t=t.defaultValue,r!=null){if(t!=null)throw Error(S(92));if(Ir(r)){if(1<r.length)throw Error(S(93));r=r[0]}t=r}t==null&&(t=""),r=t}e._wrapperState={initialValue:Ct(r)}}function Pc(e,t){var r=Ct(t.value),n=Ct(t.defaultValue);r!=null&&(r=""+r,r!==e.value&&(e.value=r),t.defaultValue==null&&e.defaultValue!==r&&(e.defaultValue=r)),n!=null&&(e.defaultValue=""+n)}function Hi(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function _c(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Wl(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?_c(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Ln,Tc=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,r,n,l){MSApp.execUnsafeLocalFunction(function(){return e(t,r,n,l)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Ln=Ln||document.createElement("div"),Ln.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Ln.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Kr(e,t){if(t){var r=e.firstChild;if(r&&r===e.lastChild&&r.nodeType===3){r.nodeValue=t;return}}e.textContent=t}var $r={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Em=["Webkit","ms","Moz","O"];Object.keys($r).forEach(function(e){Em.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),$r[t]=$r[e]})});function Rc(e,t,r){return t==null||typeof t=="boolean"||t===""?"":r||typeof t!="number"||t===0||$r.hasOwnProperty(e)&&$r[e]?(""+t).trim():t+"px"}function Oc(e,t){e=e.style;for(var r in t)if(t.hasOwnProperty(r)){var n=r.indexOf("--")===0,l=Rc(r,t[r],n);r==="float"&&(r="cssFloat"),n?e.setProperty(r,l):e[r]=l}}var Pm=J({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Hl(e,t){if(t){if(Pm[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(S(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(S(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(S(61))}if(t.style!=null&&typeof t.style!="object")throw Error(S(62))}}function Ql(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Yl=null;function Wa(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Jl=null,mr=null,fr=null;function Qi(e){if(e=wn(e)){if(typeof Jl!="function")throw Error(S(280));var t=e.stateNode;t&&(t=Us(t),Jl(e.stateNode,e.type,t))}}function Lc(e){mr?fr?fr.push(e):fr=[e]:mr=e}function Mc(){if(mr){var e=mr,t=fr;if(fr=mr=null,Qi(e),t)for(e=0;e<t.length;e++)Qi(t[e])}}function zc(e,t){return e(t)}function Ac(){}var ol=!1;function Ic(e,t,r){if(ol)return e(t,r);ol=!0;try{return zc(e,t,r)}finally{ol=!1,(mr!==null||fr!==null)&&(Ac(),Mc())}}function qr(e,t){var r=e.stateNode;if(r===null)return null;var n=Us(r);if(n===null)return null;r=n[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(n=!n.disabled)||(e=e.type,n=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!n;break e;default:e=!1}if(e)return null;if(r&&typeof r!="function")throw Error(S(231,t,typeof r));return r}var Gl=!1;if(rt)try{var _r={};Object.defineProperty(_r,"passive",{get:function(){Gl=!0}}),window.addEventListener("test",_r,_r),window.removeEventListener("test",_r,_r)}catch{Gl=!1}function _m(e,t,r,n,l,a,i,o,c){var u=Array.prototype.slice.call(arguments,3);try{t.apply(r,u)}catch(f){this.onError(f)}}var Ur=!1,as=null,is=!1,Kl=null,Tm={onError:function(e){Ur=!0,as=e}};function Rm(e,t,r,n,l,a,i,o,c){Ur=!1,as=null,_m.apply(Tm,arguments)}function Om(e,t,r,n,l,a,i,o,c){if(Rm.apply(this,arguments),Ur){if(Ur){var u=as;Ur=!1,as=null}else throw Error(S(198));is||(is=!0,Kl=u)}}function Qt(e){var t=e,r=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(r=t.return),e=t.return;while(e)}return t.tag===3?r:null}function Dc(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Yi(e){if(Qt(e)!==e)throw Error(S(188))}function Lm(e){var t=e.alternate;if(!t){if(t=Qt(e),t===null)throw Error(S(188));return t!==e?null:e}for(var r=e,n=t;;){var l=r.return;if(l===null)break;var a=l.alternate;if(a===null){if(n=l.return,n!==null){r=n;continue}break}if(l.child===a.child){for(a=l.child;a;){if(a===r)return Yi(l),e;if(a===n)return Yi(l),t;a=a.sibling}throw Error(S(188))}if(r.return!==n.return)r=l,n=a;else{for(var i=!1,o=l.child;o;){if(o===r){i=!0,r=l,n=a;break}if(o===n){i=!0,n=l,r=a;break}o=o.sibling}if(!i){for(o=a.child;o;){if(o===r){i=!0,r=a,n=l;break}if(o===n){i=!0,n=a,r=l;break}o=o.sibling}if(!i)throw Error(S(189))}}if(r.alternate!==n)throw Error(S(190))}if(r.tag!==3)throw Error(S(188));return r.stateNode.current===r?e:t}function Fc(e){return e=Lm(e),e!==null?$c(e):null}function $c(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=$c(e);if(t!==null)return t;e=e.sibling}return null}var Uc=Ee.unstable_scheduleCallback,Ji=Ee.unstable_cancelCallback,Mm=Ee.unstable_shouldYield,zm=Ee.unstable_requestPaint,q=Ee.unstable_now,Am=Ee.unstable_getCurrentPriorityLevel,Ha=Ee.unstable_ImmediatePriority,Bc=Ee.unstable_UserBlockingPriority,os=Ee.unstable_NormalPriority,Im=Ee.unstable_LowPriority,Vc=Ee.unstable_IdlePriority,Is=null,Je=null;function Dm(e){if(Je&&typeof Je.onCommitFiberRoot=="function")try{Je.onCommitFiberRoot(Is,e,void 0,(e.current.flags&128)===128)}catch{}}var $e=Math.clz32?Math.clz32:Um,Fm=Math.log,$m=Math.LN2;function Um(e){return e>>>=0,e===0?32:31-(Fm(e)/$m|0)|0}var Mn=64,zn=4194304;function Dr(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function cs(e,t){var r=e.pendingLanes;if(r===0)return 0;var n=0,l=e.suspendedLanes,a=e.pingedLanes,i=r&268435455;if(i!==0){var o=i&~l;o!==0?n=Dr(o):(a&=i,a!==0&&(n=Dr(a)))}else i=r&~l,i!==0?n=Dr(i):a!==0&&(n=Dr(a));if(n===0)return 0;if(t!==0&&t!==n&&!(t&l)&&(l=n&-n,a=t&-t,l>=a||l===16&&(a&4194240)!==0))return t;if(n&4&&(n|=r&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=n;0<t;)r=31-$e(t),l=1<<r,n|=e[r],t&=~l;return n}function Bm(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Vm(e,t){for(var r=e.suspendedLanes,n=e.pingedLanes,l=e.expirationTimes,a=e.pendingLanes;0<a;){var i=31-$e(a),o=1<<i,c=l[i];c===-1?(!(o&r)||o&n)&&(l[i]=Bm(o,t)):c<=t&&(e.expiredLanes|=o),a&=~o}}function ql(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Wc(){var e=Mn;return Mn<<=1,!(Mn&4194240)&&(Mn=64),e}function cl(e){for(var t=[],r=0;31>r;r++)t.push(e);return t}function jn(e,t,r){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-$e(t),e[t]=r}function Wm(e,t){var r=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var n=e.eventTimes;for(e=e.expirationTimes;0<r;){var l=31-$e(r),a=1<<l;t[l]=0,n[l]=-1,e[l]=-1,r&=~a}}function Qa(e,t){var r=e.entangledLanes|=t;for(e=e.entanglements;r;){var n=31-$e(r),l=1<<n;l&t|e[n]&t&&(e[n]|=t),r&=~l}}var D=0;function Hc(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Qc,Ya,Yc,Jc,Gc,Xl=!1,An=[],xt=null,gt=null,yt=null,Xr=new Map,Zr=new Map,dt=[],Hm="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Gi(e,t){switch(e){case"focusin":case"focusout":xt=null;break;case"dragenter":case"dragleave":gt=null;break;case"mouseover":case"mouseout":yt=null;break;case"pointerover":case"pointerout":Xr.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Zr.delete(t.pointerId)}}function Tr(e,t,r,n,l,a){return e===null||e.nativeEvent!==a?(e={blockedOn:t,domEventName:r,eventSystemFlags:n,nativeEvent:a,targetContainers:[l]},t!==null&&(t=wn(t),t!==null&&Ya(t)),e):(e.eventSystemFlags|=n,t=e.targetContainers,l!==null&&t.indexOf(l)===-1&&t.push(l),e)}function Qm(e,t,r,n,l){switch(t){case"focusin":return xt=Tr(xt,e,t,r,n,l),!0;case"dragenter":return gt=Tr(gt,e,t,r,n,l),!0;case"mouseover":return yt=Tr(yt,e,t,r,n,l),!0;case"pointerover":var a=l.pointerId;return Xr.set(a,Tr(Xr.get(a)||null,e,t,r,n,l)),!0;case"gotpointercapture":return a=l.pointerId,Zr.set(a,Tr(Zr.get(a)||null,e,t,r,n,l)),!0}return!1}function Kc(e){var t=Mt(e.target);if(t!==null){var r=Qt(t);if(r!==null){if(t=r.tag,t===13){if(t=Dc(r),t!==null){e.blockedOn=t,Gc(e.priority,function(){Yc(r)});return}}else if(t===3&&r.stateNode.current.memoizedState.isDehydrated){e.blockedOn=r.tag===3?r.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Gn(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var r=Zl(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(r===null){r=e.nativeEvent;var n=new r.constructor(r.type,r);Yl=n,r.target.dispatchEvent(n),Yl=null}else return t=wn(r),t!==null&&Ya(t),e.blockedOn=r,!1;t.shift()}return!0}function Ki(e,t,r){Gn(e)&&r.delete(t)}function Ym(){Xl=!1,xt!==null&&Gn(xt)&&(xt=null),gt!==null&&Gn(gt)&&(gt=null),yt!==null&&Gn(yt)&&(yt=null),Xr.forEach(Ki),Zr.forEach(Ki)}function Rr(e,t){e.blockedOn===t&&(e.blockedOn=null,Xl||(Xl=!0,Ee.unstable_scheduleCallback(Ee.unstable_NormalPriority,Ym)))}function en(e){function t(l){return Rr(l,e)}if(0<An.length){Rr(An[0],e);for(var r=1;r<An.length;r++){var n=An[r];n.blockedOn===e&&(n.blockedOn=null)}}for(xt!==null&&Rr(xt,e),gt!==null&&Rr(gt,e),yt!==null&&Rr(yt,e),Xr.forEach(t),Zr.forEach(t),r=0;r<dt.length;r++)n=dt[r],n.blockedOn===e&&(n.blockedOn=null);for(;0<dt.length&&(r=dt[0],r.blockedOn===null);)Kc(r),r.blockedOn===null&&dt.shift()}var pr=at.ReactCurrentBatchConfig,us=!0;function Jm(e,t,r,n){var l=D,a=pr.transition;pr.transition=null;try{D=1,Ja(e,t,r,n)}finally{D=l,pr.transition=a}}function Gm(e,t,r,n){var l=D,a=pr.transition;pr.transition=null;try{D=4,Ja(e,t,r,n)}finally{D=l,pr.transition=a}}function Ja(e,t,r,n){if(us){var l=Zl(e,t,r,n);if(l===null)vl(e,t,n,ds,r),Gi(e,n);else if(Qm(l,e,t,r,n))n.stopPropagation();else if(Gi(e,n),t&4&&-1<Hm.indexOf(e)){for(;l!==null;){var a=wn(l);if(a!==null&&Qc(a),a=Zl(e,t,r,n),a===null&&vl(e,t,n,ds,r),a===l)break;l=a}l!==null&&n.stopPropagation()}else vl(e,t,n,null,r)}}var ds=null;function Zl(e,t,r,n){if(ds=null,e=Wa(n),e=Mt(e),e!==null)if(t=Qt(e),t===null)e=null;else if(r=t.tag,r===13){if(e=Dc(t),e!==null)return e;e=null}else if(r===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return ds=e,null}function qc(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Am()){case Ha:return 1;case Bc:return 4;case os:case Im:return 16;case Vc:return 536870912;default:return 16}default:return 16}}var ft=null,Ga=null,Kn=null;function Xc(){if(Kn)return Kn;var e,t=Ga,r=t.length,n,l="value"in ft?ft.value:ft.textContent,a=l.length;for(e=0;e<r&&t[e]===l[e];e++);var i=r-e;for(n=1;n<=i&&t[r-n]===l[a-n];n++);return Kn=l.slice(e,1<n?1-n:void 0)}function qn(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function In(){return!0}function qi(){return!1}function _e(e){function t(r,n,l,a,i){this._reactName=r,this._targetInst=l,this.type=n,this.nativeEvent=a,this.target=i,this.currentTarget=null;for(var o in e)e.hasOwnProperty(o)&&(r=e[o],this[o]=r?r(a):a[o]);return this.isDefaultPrevented=(a.defaultPrevented!=null?a.defaultPrevented:a.returnValue===!1)?In:qi,this.isPropagationStopped=qi,this}return J(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var r=this.nativeEvent;r&&(r.preventDefault?r.preventDefault():typeof r.returnValue!="unknown"&&(r.returnValue=!1),this.isDefaultPrevented=In)},stopPropagation:function(){var r=this.nativeEvent;r&&(r.stopPropagation?r.stopPropagation():typeof r.cancelBubble!="unknown"&&(r.cancelBubble=!0),this.isPropagationStopped=In)},persist:function(){},isPersistent:In}),t}var br={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Ka=_e(br),Nn=J({},br,{view:0,detail:0}),Km=_e(Nn),ul,dl,Or,Ds=J({},Nn,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:qa,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Or&&(Or&&e.type==="mousemove"?(ul=e.screenX-Or.screenX,dl=e.screenY-Or.screenY):dl=ul=0,Or=e),ul)},movementY:function(e){return"movementY"in e?e.movementY:dl}}),Xi=_e(Ds),qm=J({},Ds,{dataTransfer:0}),Xm=_e(qm),Zm=J({},Nn,{relatedTarget:0}),ml=_e(Zm),ef=J({},br,{animationName:0,elapsedTime:0,pseudoElement:0}),tf=_e(ef),rf=J({},br,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),nf=_e(rf),sf=J({},br,{data:0}),Zi=_e(sf),lf={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},af={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},of={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function cf(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=of[e])?!!t[e]:!1}function qa(){return cf}var uf=J({},Nn,{key:function(e){if(e.key){var t=lf[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=qn(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?af[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:qa,charCode:function(e){return e.type==="keypress"?qn(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?qn(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),df=_e(uf),mf=J({},Ds,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),eo=_e(mf),ff=J({},Nn,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:qa}),pf=_e(ff),hf=J({},br,{propertyName:0,elapsedTime:0,pseudoElement:0}),xf=_e(hf),gf=J({},Ds,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),yf=_e(gf),vf=[9,13,27,32],Xa=rt&&"CompositionEvent"in window,Br=null;rt&&"documentMode"in document&&(Br=document.documentMode);var jf=rt&&"TextEvent"in window&&!Br,Zc=rt&&(!Xa||Br&&8<Br&&11>=Br),to=String.fromCharCode(32),ro=!1;function eu(e,t){switch(e){case"keyup":return vf.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function tu(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var tr=!1;function Nf(e,t){switch(e){case"compositionend":return tu(t);case"keypress":return t.which!==32?null:(ro=!0,to);case"textInput":return e=t.data,e===to&&ro?null:e;default:return null}}function wf(e,t){if(tr)return e==="compositionend"||!Xa&&eu(e,t)?(e=Xc(),Kn=Ga=ft=null,tr=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Zc&&t.locale!=="ko"?null:t.data;default:return null}}var Sf={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function no(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Sf[e.type]:t==="textarea"}function ru(e,t,r,n){Lc(n),t=ms(t,"onChange"),0<t.length&&(r=new Ka("onChange","change",null,r,n),e.push({event:r,listeners:t}))}var Vr=null,tn=null;function kf(e){fu(e,0)}function Fs(e){var t=sr(e);if(bc(t))return e}function Cf(e,t){if(e==="change")return t}var nu=!1;if(rt){var fl;if(rt){var pl="oninput"in document;if(!pl){var so=document.createElement("div");so.setAttribute("oninput","return;"),pl=typeof so.oninput=="function"}fl=pl}else fl=!1;nu=fl&&(!document.documentMode||9<document.documentMode)}function lo(){Vr&&(Vr.detachEvent("onpropertychange",su),tn=Vr=null)}function su(e){if(e.propertyName==="value"&&Fs(tn)){var t=[];ru(t,tn,e,Wa(e)),Ic(kf,t)}}function bf(e,t,r){e==="focusin"?(lo(),Vr=t,tn=r,Vr.attachEvent("onpropertychange",su)):e==="focusout"&&lo()}function Ef(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Fs(tn)}function Pf(e,t){if(e==="click")return Fs(t)}function _f(e,t){if(e==="input"||e==="change")return Fs(t)}function Tf(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Be=typeof Object.is=="function"?Object.is:Tf;function rn(e,t){if(Be(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var r=Object.keys(e),n=Object.keys(t);if(r.length!==n.length)return!1;for(n=0;n<r.length;n++){var l=r[n];if(!zl.call(t,l)||!Be(e[l],t[l]))return!1}return!0}function ao(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function io(e,t){var r=ao(e);e=0;for(var n;r;){if(r.nodeType===3){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=ao(r)}}function lu(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?lu(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function au(){for(var e=window,t=ls();t instanceof e.HTMLIFrameElement;){try{var r=typeof t.contentWindow.location.href=="string"}catch{r=!1}if(r)e=t.contentWindow;else break;t=ls(e.document)}return t}function Za(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function Rf(e){var t=au(),r=e.focusedElem,n=e.selectionRange;if(t!==r&&r&&r.ownerDocument&&lu(r.ownerDocument.documentElement,r)){if(n!==null&&Za(r)){if(t=n.start,e=n.end,e===void 0&&(e=t),"selectionStart"in r)r.selectionStart=t,r.selectionEnd=Math.min(e,r.value.length);else if(e=(t=r.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var l=r.textContent.length,a=Math.min(n.start,l);n=n.end===void 0?a:Math.min(n.end,l),!e.extend&&a>n&&(l=n,n=a,a=l),l=io(r,a);var i=io(r,n);l&&i&&(e.rangeCount!==1||e.anchorNode!==l.node||e.anchorOffset!==l.offset||e.focusNode!==i.node||e.focusOffset!==i.offset)&&(t=t.createRange(),t.setStart(l.node,l.offset),e.removeAllRanges(),a>n?(e.addRange(t),e.extend(i.node,i.offset)):(t.setEnd(i.node,i.offset),e.addRange(t)))}}for(t=[],e=r;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof r.focus=="function"&&r.focus(),r=0;r<t.length;r++)e=t[r],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Of=rt&&"documentMode"in document&&11>=document.documentMode,rr=null,ea=null,Wr=null,ta=!1;function oo(e,t,r){var n=r.window===r?r.document:r.nodeType===9?r:r.ownerDocument;ta||rr==null||rr!==ls(n)||(n=rr,"selectionStart"in n&&Za(n)?n={start:n.selectionStart,end:n.selectionEnd}:(n=(n.ownerDocument&&n.ownerDocument.defaultView||window).getSelection(),n={anchorNode:n.anchorNode,anchorOffset:n.anchorOffset,focusNode:n.focusNode,focusOffset:n.focusOffset}),Wr&&rn(Wr,n)||(Wr=n,n=ms(ea,"onSelect"),0<n.length&&(t=new Ka("onSelect","select",null,t,r),e.push({event:t,listeners:n}),t.target=rr)))}function Dn(e,t){var r={};return r[e.toLowerCase()]=t.toLowerCase(),r["Webkit"+e]="webkit"+t,r["Moz"+e]="moz"+t,r}var nr={animationend:Dn("Animation","AnimationEnd"),animationiteration:Dn("Animation","AnimationIteration"),animationstart:Dn("Animation","AnimationStart"),transitionend:Dn("Transition","TransitionEnd")},hl={},iu={};rt&&(iu=document.createElement("div").style,"AnimationEvent"in window||(delete nr.animationend.animation,delete nr.animationiteration.animation,delete nr.animationstart.animation),"TransitionEvent"in window||delete nr.transitionend.transition);function $s(e){if(hl[e])return hl[e];if(!nr[e])return e;var t=nr[e],r;for(r in t)if(t.hasOwnProperty(r)&&r in iu)return hl[e]=t[r];return e}var ou=$s("animationend"),cu=$s("animationiteration"),uu=$s("animationstart"),du=$s("transitionend"),mu=new Map,co="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Et(e,t){mu.set(e,t),Ht(t,[e])}for(var xl=0;xl<co.length;xl++){var gl=co[xl],Lf=gl.toLowerCase(),Mf=gl[0].toUpperCase()+gl.slice(1);Et(Lf,"on"+Mf)}Et(ou,"onAnimationEnd");Et(cu,"onAnimationIteration");Et(uu,"onAnimationStart");Et("dblclick","onDoubleClick");Et("focusin","onFocus");Et("focusout","onBlur");Et(du,"onTransitionEnd");gr("onMouseEnter",["mouseout","mouseover"]);gr("onMouseLeave",["mouseout","mouseover"]);gr("onPointerEnter",["pointerout","pointerover"]);gr("onPointerLeave",["pointerout","pointerover"]);Ht("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Ht("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Ht("onBeforeInput",["compositionend","keypress","textInput","paste"]);Ht("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Ht("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Ht("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Fr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),zf=new Set("cancel close invalid load scroll toggle".split(" ").concat(Fr));function uo(e,t,r){var n=e.type||"unknown-event";e.currentTarget=r,Om(n,t,void 0,e),e.currentTarget=null}function fu(e,t){t=(t&4)!==0;for(var r=0;r<e.length;r++){var n=e[r],l=n.event;n=n.listeners;e:{var a=void 0;if(t)for(var i=n.length-1;0<=i;i--){var o=n[i],c=o.instance,u=o.currentTarget;if(o=o.listener,c!==a&&l.isPropagationStopped())break e;uo(l,o,u),a=c}else for(i=0;i<n.length;i++){if(o=n[i],c=o.instance,u=o.currentTarget,o=o.listener,c!==a&&l.isPropagationStopped())break e;uo(l,o,u),a=c}}}if(is)throw e=Kl,is=!1,Kl=null,e}function U(e,t){var r=t[aa];r===void 0&&(r=t[aa]=new Set);var n=e+"__bubble";r.has(n)||(pu(t,e,2,!1),r.add(n))}function yl(e,t,r){var n=0;t&&(n|=4),pu(r,e,n,t)}var Fn="_reactListening"+Math.random().toString(36).slice(2);function nn(e){if(!e[Fn]){e[Fn]=!0,Nc.forEach(function(r){r!=="selectionchange"&&(zf.has(r)||yl(r,!1,e),yl(r,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Fn]||(t[Fn]=!0,yl("selectionchange",!1,t))}}function pu(e,t,r,n){switch(qc(t)){case 1:var l=Jm;break;case 4:l=Gm;break;default:l=Ja}r=l.bind(null,t,r,e),l=void 0,!Gl||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(l=!0),n?l!==void 0?e.addEventListener(t,r,{capture:!0,passive:l}):e.addEventListener(t,r,!0):l!==void 0?e.addEventListener(t,r,{passive:l}):e.addEventListener(t,r,!1)}function vl(e,t,r,n,l){var a=n;if(!(t&1)&&!(t&2)&&n!==null)e:for(;;){if(n===null)return;var i=n.tag;if(i===3||i===4){var o=n.stateNode.containerInfo;if(o===l||o.nodeType===8&&o.parentNode===l)break;if(i===4)for(i=n.return;i!==null;){var c=i.tag;if((c===3||c===4)&&(c=i.stateNode.containerInfo,c===l||c.nodeType===8&&c.parentNode===l))return;i=i.return}for(;o!==null;){if(i=Mt(o),i===null)return;if(c=i.tag,c===5||c===6){n=a=i;continue e}o=o.parentNode}}n=n.return}Ic(function(){var u=a,f=Wa(r),m=[];e:{var h=mu.get(e);if(h!==void 0){var g=Ka,j=e;switch(e){case"keypress":if(qn(r)===0)break e;case"keydown":case"keyup":g=df;break;case"focusin":j="focus",g=ml;break;case"focusout":j="blur",g=ml;break;case"beforeblur":case"afterblur":g=ml;break;case"click":if(r.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":g=Xi;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":g=Xm;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":g=pf;break;case ou:case cu:case uu:g=tf;break;case du:g=xf;break;case"scroll":g=Km;break;case"wheel":g=yf;break;case"copy":case"cut":case"paste":g=nf;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":g=eo}var y=(t&4)!==0,w=!y&&e==="scroll",p=y?h!==null?h+"Capture":null:h;y=[];for(var d=u,x;d!==null;){x=d;var v=x.stateNode;if(x.tag===5&&v!==null&&(x=v,p!==null&&(v=qr(d,p),v!=null&&y.push(sn(d,v,x)))),w)break;d=d.return}0<y.length&&(h=new g(h,j,null,r,f),m.push({event:h,listeners:y}))}}if(!(t&7)){e:{if(h=e==="mouseover"||e==="pointerover",g=e==="mouseout"||e==="pointerout",h&&r!==Yl&&(j=r.relatedTarget||r.fromElement)&&(Mt(j)||j[nt]))break e;if((g||h)&&(h=f.window===f?f:(h=f.ownerDocument)?h.defaultView||h.parentWindow:window,g?(j=r.relatedTarget||r.toElement,g=u,j=j?Mt(j):null,j!==null&&(w=Qt(j),j!==w||j.tag!==5&&j.tag!==6)&&(j=null)):(g=null,j=u),g!==j)){if(y=Xi,v="onMouseLeave",p="onMouseEnter",d="mouse",(e==="pointerout"||e==="pointerover")&&(y=eo,v="onPointerLeave",p="onPointerEnter",d="pointer"),w=g==null?h:sr(g),x=j==null?h:sr(j),h=new y(v,d+"leave",g,r,f),h.target=w,h.relatedTarget=x,v=null,Mt(f)===u&&(y=new y(p,d+"enter",j,r,f),y.target=x,y.relatedTarget=w,v=y),w=v,g&&j)t:{for(y=g,p=j,d=0,x=y;x;x=qt(x))d++;for(x=0,v=p;v;v=qt(v))x++;for(;0<d-x;)y=qt(y),d--;for(;0<x-d;)p=qt(p),x--;for(;d--;){if(y===p||p!==null&&y===p.alternate)break t;y=qt(y),p=qt(p)}y=null}else y=null;g!==null&&mo(m,h,g,y,!1),j!==null&&w!==null&&mo(m,w,j,y,!0)}}e:{if(h=u?sr(u):window,g=h.nodeName&&h.nodeName.toLowerCase(),g==="select"||g==="input"&&h.type==="file")var k=Cf;else if(no(h))if(nu)k=_f;else{k=Ef;var C=bf}else(g=h.nodeName)&&g.toLowerCase()==="input"&&(h.type==="checkbox"||h.type==="radio")&&(k=Pf);if(k&&(k=k(e,u))){ru(m,k,r,f);break e}C&&C(e,h,u),e==="focusout"&&(C=h._wrapperState)&&C.controlled&&h.type==="number"&&Bl(h,"number",h.value)}switch(C=u?sr(u):window,e){case"focusin":(no(C)||C.contentEditable==="true")&&(rr=C,ea=u,Wr=null);break;case"focusout":Wr=ea=rr=null;break;case"mousedown":ta=!0;break;case"contextmenu":case"mouseup":case"dragend":ta=!1,oo(m,r,f);break;case"selectionchange":if(Of)break;case"keydown":case"keyup":oo(m,r,f)}var E;if(Xa)e:{switch(e){case"compositionstart":var T="onCompositionStart";break e;case"compositionend":T="onCompositionEnd";break e;case"compositionupdate":T="onCompositionUpdate";break e}T=void 0}else tr?eu(e,r)&&(T="onCompositionEnd"):e==="keydown"&&r.keyCode===229&&(T="onCompositionStart");T&&(Zc&&r.locale!=="ko"&&(tr||T!=="onCompositionStart"?T==="onCompositionEnd"&&tr&&(E=Xc()):(ft=f,Ga="value"in ft?ft.value:ft.textContent,tr=!0)),C=ms(u,T),0<C.length&&(T=new Zi(T,e,null,r,f),m.push({event:T,listeners:C}),E?T.data=E:(E=tu(r),E!==null&&(T.data=E)))),(E=jf?Nf(e,r):wf(e,r))&&(u=ms(u,"onBeforeInput"),0<u.length&&(f=new Zi("onBeforeInput","beforeinput",null,r,f),m.push({event:f,listeners:u}),f.data=E))}fu(m,t)})}function sn(e,t,r){return{instance:e,listener:t,currentTarget:r}}function ms(e,t){for(var r=t+"Capture",n=[];e!==null;){var l=e,a=l.stateNode;l.tag===5&&a!==null&&(l=a,a=qr(e,r),a!=null&&n.unshift(sn(e,a,l)),a=qr(e,t),a!=null&&n.push(sn(e,a,l))),e=e.return}return n}function qt(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function mo(e,t,r,n,l){for(var a=t._reactName,i=[];r!==null&&r!==n;){var o=r,c=o.alternate,u=o.stateNode;if(c!==null&&c===n)break;o.tag===5&&u!==null&&(o=u,l?(c=qr(r,a),c!=null&&i.unshift(sn(r,c,o))):l||(c=qr(r,a),c!=null&&i.push(sn(r,c,o)))),r=r.return}i.length!==0&&e.push({event:t,listeners:i})}var Af=/\r\n?/g,If=/\u0000|\uFFFD/g;function fo(e){return(typeof e=="string"?e:""+e).replace(Af,`
`).replace(If,"")}function $n(e,t,r){if(t=fo(t),fo(e)!==t&&r)throw Error(S(425))}function fs(){}var ra=null,na=null;function sa(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var la=typeof setTimeout=="function"?setTimeout:void 0,Df=typeof clearTimeout=="function"?clearTimeout:void 0,po=typeof Promise=="function"?Promise:void 0,Ff=typeof queueMicrotask=="function"?queueMicrotask:typeof po<"u"?function(e){return po.resolve(null).then(e).catch($f)}:la;function $f(e){setTimeout(function(){throw e})}function jl(e,t){var r=t,n=0;do{var l=r.nextSibling;if(e.removeChild(r),l&&l.nodeType===8)if(r=l.data,r==="/$"){if(n===0){e.removeChild(l),en(t);return}n--}else r!=="$"&&r!=="$?"&&r!=="$!"||n++;r=l}while(r);en(t)}function vt(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function ho(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var r=e.data;if(r==="$"||r==="$!"||r==="$?"){if(t===0)return e;t--}else r==="/$"&&t++}e=e.previousSibling}return null}var Er=Math.random().toString(36).slice(2),Ye="__reactFiber$"+Er,ln="__reactProps$"+Er,nt="__reactContainer$"+Er,aa="__reactEvents$"+Er,Uf="__reactListeners$"+Er,Bf="__reactHandles$"+Er;function Mt(e){var t=e[Ye];if(t)return t;for(var r=e.parentNode;r;){if(t=r[nt]||r[Ye]){if(r=t.alternate,t.child!==null||r!==null&&r.child!==null)for(e=ho(e);e!==null;){if(r=e[Ye])return r;e=ho(e)}return t}e=r,r=e.parentNode}return null}function wn(e){return e=e[Ye]||e[nt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function sr(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(S(33))}function Us(e){return e[ln]||null}var ia=[],lr=-1;function Pt(e){return{current:e}}function B(e){0>lr||(e.current=ia[lr],ia[lr]=null,lr--)}function $(e,t){lr++,ia[lr]=e.current,e.current=t}var bt={},de=Pt(bt),Ne=Pt(!1),Ft=bt;function yr(e,t){var r=e.type.contextTypes;if(!r)return bt;var n=e.stateNode;if(n&&n.__reactInternalMemoizedUnmaskedChildContext===t)return n.__reactInternalMemoizedMaskedChildContext;var l={},a;for(a in r)l[a]=t[a];return n&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=l),l}function we(e){return e=e.childContextTypes,e!=null}function ps(){B(Ne),B(de)}function xo(e,t,r){if(de.current!==bt)throw Error(S(168));$(de,t),$(Ne,r)}function hu(e,t,r){var n=e.stateNode;if(t=t.childContextTypes,typeof n.getChildContext!="function")return r;n=n.getChildContext();for(var l in n)if(!(l in t))throw Error(S(108,Cm(e)||"Unknown",l));return J({},r,n)}function hs(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||bt,Ft=de.current,$(de,e),$(Ne,Ne.current),!0}function go(e,t,r){var n=e.stateNode;if(!n)throw Error(S(169));r?(e=hu(e,t,Ft),n.__reactInternalMemoizedMergedChildContext=e,B(Ne),B(de),$(de,e)):B(Ne),$(Ne,r)}var Xe=null,Bs=!1,Nl=!1;function xu(e){Xe===null?Xe=[e]:Xe.push(e)}function Vf(e){Bs=!0,xu(e)}function _t(){if(!Nl&&Xe!==null){Nl=!0;var e=0,t=D;try{var r=Xe;for(D=1;e<r.length;e++){var n=r[e];do n=n(!0);while(n!==null)}Xe=null,Bs=!1}catch(l){throw Xe!==null&&(Xe=Xe.slice(e+1)),Uc(Ha,_t),l}finally{D=t,Nl=!1}}return null}var ar=[],ir=0,xs=null,gs=0,Te=[],Re=0,$t=null,Ze=1,et="";function Ot(e,t){ar[ir++]=gs,ar[ir++]=xs,xs=e,gs=t}function gu(e,t,r){Te[Re++]=Ze,Te[Re++]=et,Te[Re++]=$t,$t=e;var n=Ze;e=et;var l=32-$e(n)-1;n&=~(1<<l),r+=1;var a=32-$e(t)+l;if(30<a){var i=l-l%5;a=(n&(1<<i)-1).toString(32),n>>=i,l-=i,Ze=1<<32-$e(t)+l|r<<l|n,et=a+e}else Ze=1<<a|r<<l|n,et=e}function ei(e){e.return!==null&&(Ot(e,1),gu(e,1,0))}function ti(e){for(;e===xs;)xs=ar[--ir],ar[ir]=null,gs=ar[--ir],ar[ir]=null;for(;e===$t;)$t=Te[--Re],Te[Re]=null,et=Te[--Re],Te[Re]=null,Ze=Te[--Re],Te[Re]=null}var be=null,Ce=null,W=!1,Fe=null;function yu(e,t){var r=Oe(5,null,null,0);r.elementType="DELETED",r.stateNode=t,r.return=e,t=e.deletions,t===null?(e.deletions=[r],e.flags|=16):t.push(r)}function yo(e,t){switch(e.tag){case 5:var r=e.type;return t=t.nodeType!==1||r.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,be=e,Ce=vt(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,be=e,Ce=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(r=$t!==null?{id:Ze,overflow:et}:null,e.memoizedState={dehydrated:t,treeContext:r,retryLane:1073741824},r=Oe(18,null,null,0),r.stateNode=t,r.return=e,e.child=r,be=e,Ce=null,!0):!1;default:return!1}}function oa(e){return(e.mode&1)!==0&&(e.flags&128)===0}function ca(e){if(W){var t=Ce;if(t){var r=t;if(!yo(e,t)){if(oa(e))throw Error(S(418));t=vt(r.nextSibling);var n=be;t&&yo(e,t)?yu(n,r):(e.flags=e.flags&-4097|2,W=!1,be=e)}}else{if(oa(e))throw Error(S(418));e.flags=e.flags&-4097|2,W=!1,be=e}}}function vo(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;be=e}function Un(e){if(e!==be)return!1;if(!W)return vo(e),W=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!sa(e.type,e.memoizedProps)),t&&(t=Ce)){if(oa(e))throw vu(),Error(S(418));for(;t;)yu(e,t),t=vt(t.nextSibling)}if(vo(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(S(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var r=e.data;if(r==="/$"){if(t===0){Ce=vt(e.nextSibling);break e}t--}else r!=="$"&&r!=="$!"&&r!=="$?"||t++}e=e.nextSibling}Ce=null}}else Ce=be?vt(e.stateNode.nextSibling):null;return!0}function vu(){for(var e=Ce;e;)e=vt(e.nextSibling)}function vr(){Ce=be=null,W=!1}function ri(e){Fe===null?Fe=[e]:Fe.push(e)}var Wf=at.ReactCurrentBatchConfig;function Lr(e,t,r){if(e=r.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(r._owner){if(r=r._owner,r){if(r.tag!==1)throw Error(S(309));var n=r.stateNode}if(!n)throw Error(S(147,e));var l=n,a=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===a?t.ref:(t=function(i){var o=l.refs;i===null?delete o[a]:o[a]=i},t._stringRef=a,t)}if(typeof e!="string")throw Error(S(284));if(!r._owner)throw Error(S(290,e))}return e}function Bn(e,t){throw e=Object.prototype.toString.call(t),Error(S(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function jo(e){var t=e._init;return t(e._payload)}function ju(e){function t(p,d){if(e){var x=p.deletions;x===null?(p.deletions=[d],p.flags|=16):x.push(d)}}function r(p,d){if(!e)return null;for(;d!==null;)t(p,d),d=d.sibling;return null}function n(p,d){for(p=new Map;d!==null;)d.key!==null?p.set(d.key,d):p.set(d.index,d),d=d.sibling;return p}function l(p,d){return p=St(p,d),p.index=0,p.sibling=null,p}function a(p,d,x){return p.index=x,e?(x=p.alternate,x!==null?(x=x.index,x<d?(p.flags|=2,d):x):(p.flags|=2,d)):(p.flags|=1048576,d)}function i(p){return e&&p.alternate===null&&(p.flags|=2),p}function o(p,d,x,v){return d===null||d.tag!==6?(d=Pl(x,p.mode,v),d.return=p,d):(d=l(d,x),d.return=p,d)}function c(p,d,x,v){var k=x.type;return k===er?f(p,d,x.props.children,v,x.key):d!==null&&(d.elementType===k||typeof k=="object"&&k!==null&&k.$$typeof===ct&&jo(k)===d.type)?(v=l(d,x.props),v.ref=Lr(p,d,x),v.return=p,v):(v=ss(x.type,x.key,x.props,null,p.mode,v),v.ref=Lr(p,d,x),v.return=p,v)}function u(p,d,x,v){return d===null||d.tag!==4||d.stateNode.containerInfo!==x.containerInfo||d.stateNode.implementation!==x.implementation?(d=_l(x,p.mode,v),d.return=p,d):(d=l(d,x.children||[]),d.return=p,d)}function f(p,d,x,v,k){return d===null||d.tag!==7?(d=Dt(x,p.mode,v,k),d.return=p,d):(d=l(d,x),d.return=p,d)}function m(p,d,x){if(typeof d=="string"&&d!==""||typeof d=="number")return d=Pl(""+d,p.mode,x),d.return=p,d;if(typeof d=="object"&&d!==null){switch(d.$$typeof){case Rn:return x=ss(d.type,d.key,d.props,null,p.mode,x),x.ref=Lr(p,null,d),x.return=p,x;case Zt:return d=_l(d,p.mode,x),d.return=p,d;case ct:var v=d._init;return m(p,v(d._payload),x)}if(Ir(d)||Pr(d))return d=Dt(d,p.mode,x,null),d.return=p,d;Bn(p,d)}return null}function h(p,d,x,v){var k=d!==null?d.key:null;if(typeof x=="string"&&x!==""||typeof x=="number")return k!==null?null:o(p,d,""+x,v);if(typeof x=="object"&&x!==null){switch(x.$$typeof){case Rn:return x.key===k?c(p,d,x,v):null;case Zt:return x.key===k?u(p,d,x,v):null;case ct:return k=x._init,h(p,d,k(x._payload),v)}if(Ir(x)||Pr(x))return k!==null?null:f(p,d,x,v,null);Bn(p,x)}return null}function g(p,d,x,v,k){if(typeof v=="string"&&v!==""||typeof v=="number")return p=p.get(x)||null,o(d,p,""+v,k);if(typeof v=="object"&&v!==null){switch(v.$$typeof){case Rn:return p=p.get(v.key===null?x:v.key)||null,c(d,p,v,k);case Zt:return p=p.get(v.key===null?x:v.key)||null,u(d,p,v,k);case ct:var C=v._init;return g(p,d,x,C(v._payload),k)}if(Ir(v)||Pr(v))return p=p.get(x)||null,f(d,p,v,k,null);Bn(d,v)}return null}function j(p,d,x,v){for(var k=null,C=null,E=d,T=d=0,F=null;E!==null&&T<x.length;T++){E.index>T?(F=E,E=null):F=E.sibling;var M=h(p,E,x[T],v);if(M===null){E===null&&(E=F);break}e&&E&&M.alternate===null&&t(p,E),d=a(M,d,T),C===null?k=M:C.sibling=M,C=M,E=F}if(T===x.length)return r(p,E),W&&Ot(p,T),k;if(E===null){for(;T<x.length;T++)E=m(p,x[T],v),E!==null&&(d=a(E,d,T),C===null?k=E:C.sibling=E,C=E);return W&&Ot(p,T),k}for(E=n(p,E);T<x.length;T++)F=g(E,p,T,x[T],v),F!==null&&(e&&F.alternate!==null&&E.delete(F.key===null?T:F.key),d=a(F,d,T),C===null?k=F:C.sibling=F,C=F);return e&&E.forEach(function(_){return t(p,_)}),W&&Ot(p,T),k}function y(p,d,x,v){var k=Pr(x);if(typeof k!="function")throw Error(S(150));if(x=k.call(x),x==null)throw Error(S(151));for(var C=k=null,E=d,T=d=0,F=null,M=x.next();E!==null&&!M.done;T++,M=x.next()){E.index>T?(F=E,E=null):F=E.sibling;var _=h(p,E,M.value,v);if(_===null){E===null&&(E=F);break}e&&E&&_.alternate===null&&t(p,E),d=a(_,d,T),C===null?k=_:C.sibling=_,C=_,E=F}if(M.done)return r(p,E),W&&Ot(p,T),k;if(E===null){for(;!M.done;T++,M=x.next())M=m(p,M.value,v),M!==null&&(d=a(M,d,T),C===null?k=M:C.sibling=M,C=M);return W&&Ot(p,T),k}for(E=n(p,E);!M.done;T++,M=x.next())M=g(E,p,T,M.value,v),M!==null&&(e&&M.alternate!==null&&E.delete(M.key===null?T:M.key),d=a(M,d,T),C===null?k=M:C.sibling=M,C=M);return e&&E.forEach(function(Ve){return t(p,Ve)}),W&&Ot(p,T),k}function w(p,d,x,v){if(typeof x=="object"&&x!==null&&x.type===er&&x.key===null&&(x=x.props.children),typeof x=="object"&&x!==null){switch(x.$$typeof){case Rn:e:{for(var k=x.key,C=d;C!==null;){if(C.key===k){if(k=x.type,k===er){if(C.tag===7){r(p,C.sibling),d=l(C,x.props.children),d.return=p,p=d;break e}}else if(C.elementType===k||typeof k=="object"&&k!==null&&k.$$typeof===ct&&jo(k)===C.type){r(p,C.sibling),d=l(C,x.props),d.ref=Lr(p,C,x),d.return=p,p=d;break e}r(p,C);break}else t(p,C);C=C.sibling}x.type===er?(d=Dt(x.props.children,p.mode,v,x.key),d.return=p,p=d):(v=ss(x.type,x.key,x.props,null,p.mode,v),v.ref=Lr(p,d,x),v.return=p,p=v)}return i(p);case Zt:e:{for(C=x.key;d!==null;){if(d.key===C)if(d.tag===4&&d.stateNode.containerInfo===x.containerInfo&&d.stateNode.implementation===x.implementation){r(p,d.sibling),d=l(d,x.children||[]),d.return=p,p=d;break e}else{r(p,d);break}else t(p,d);d=d.sibling}d=_l(x,p.mode,v),d.return=p,p=d}return i(p);case ct:return C=x._init,w(p,d,C(x._payload),v)}if(Ir(x))return j(p,d,x,v);if(Pr(x))return y(p,d,x,v);Bn(p,x)}return typeof x=="string"&&x!==""||typeof x=="number"?(x=""+x,d!==null&&d.tag===6?(r(p,d.sibling),d=l(d,x),d.return=p,p=d):(r(p,d),d=Pl(x,p.mode,v),d.return=p,p=d),i(p)):r(p,d)}return w}var jr=ju(!0),Nu=ju(!1),ys=Pt(null),vs=null,or=null,ni=null;function si(){ni=or=vs=null}function li(e){var t=ys.current;B(ys),e._currentValue=t}function ua(e,t,r){for(;e!==null;){var n=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,n!==null&&(n.childLanes|=t)):n!==null&&(n.childLanes&t)!==t&&(n.childLanes|=t),e===r)break;e=e.return}}function hr(e,t){vs=e,ni=or=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(je=!0),e.firstContext=null)}function Me(e){var t=e._currentValue;if(ni!==e)if(e={context:e,memoizedValue:t,next:null},or===null){if(vs===null)throw Error(S(308));or=e,vs.dependencies={lanes:0,firstContext:e}}else or=or.next=e;return t}var zt=null;function ai(e){zt===null?zt=[e]:zt.push(e)}function wu(e,t,r,n){var l=t.interleaved;return l===null?(r.next=r,ai(t)):(r.next=l.next,l.next=r),t.interleaved=r,st(e,n)}function st(e,t){e.lanes|=t;var r=e.alternate;for(r!==null&&(r.lanes|=t),r=e,e=e.return;e!==null;)e.childLanes|=t,r=e.alternate,r!==null&&(r.childLanes|=t),r=e,e=e.return;return r.tag===3?r.stateNode:null}var ut=!1;function ii(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Su(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function tt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function jt(e,t,r){var n=e.updateQueue;if(n===null)return null;if(n=n.shared,I&2){var l=n.pending;return l===null?t.next=t:(t.next=l.next,l.next=t),n.pending=t,st(e,r)}return l=n.interleaved,l===null?(t.next=t,ai(n)):(t.next=l.next,l.next=t),n.interleaved=t,st(e,r)}function Xn(e,t,r){if(t=t.updateQueue,t!==null&&(t=t.shared,(r&4194240)!==0)){var n=t.lanes;n&=e.pendingLanes,r|=n,t.lanes=r,Qa(e,r)}}function No(e,t){var r=e.updateQueue,n=e.alternate;if(n!==null&&(n=n.updateQueue,r===n)){var l=null,a=null;if(r=r.firstBaseUpdate,r!==null){do{var i={eventTime:r.eventTime,lane:r.lane,tag:r.tag,payload:r.payload,callback:r.callback,next:null};a===null?l=a=i:a=a.next=i,r=r.next}while(r!==null);a===null?l=a=t:a=a.next=t}else l=a=t;r={baseState:n.baseState,firstBaseUpdate:l,lastBaseUpdate:a,shared:n.shared,effects:n.effects},e.updateQueue=r;return}e=r.lastBaseUpdate,e===null?r.firstBaseUpdate=t:e.next=t,r.lastBaseUpdate=t}function js(e,t,r,n){var l=e.updateQueue;ut=!1;var a=l.firstBaseUpdate,i=l.lastBaseUpdate,o=l.shared.pending;if(o!==null){l.shared.pending=null;var c=o,u=c.next;c.next=null,i===null?a=u:i.next=u,i=c;var f=e.alternate;f!==null&&(f=f.updateQueue,o=f.lastBaseUpdate,o!==i&&(o===null?f.firstBaseUpdate=u:o.next=u,f.lastBaseUpdate=c))}if(a!==null){var m=l.baseState;i=0,f=u=c=null,o=a;do{var h=o.lane,g=o.eventTime;if((n&h)===h){f!==null&&(f=f.next={eventTime:g,lane:0,tag:o.tag,payload:o.payload,callback:o.callback,next:null});e:{var j=e,y=o;switch(h=t,g=r,y.tag){case 1:if(j=y.payload,typeof j=="function"){m=j.call(g,m,h);break e}m=j;break e;case 3:j.flags=j.flags&-65537|128;case 0:if(j=y.payload,h=typeof j=="function"?j.call(g,m,h):j,h==null)break e;m=J({},m,h);break e;case 2:ut=!0}}o.callback!==null&&o.lane!==0&&(e.flags|=64,h=l.effects,h===null?l.effects=[o]:h.push(o))}else g={eventTime:g,lane:h,tag:o.tag,payload:o.payload,callback:o.callback,next:null},f===null?(u=f=g,c=m):f=f.next=g,i|=h;if(o=o.next,o===null){if(o=l.shared.pending,o===null)break;h=o,o=h.next,h.next=null,l.lastBaseUpdate=h,l.shared.pending=null}}while(1);if(f===null&&(c=m),l.baseState=c,l.firstBaseUpdate=u,l.lastBaseUpdate=f,t=l.shared.interleaved,t!==null){l=t;do i|=l.lane,l=l.next;while(l!==t)}else a===null&&(l.shared.lanes=0);Bt|=i,e.lanes=i,e.memoizedState=m}}function wo(e,t,r){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var n=e[t],l=n.callback;if(l!==null){if(n.callback=null,n=r,typeof l!="function")throw Error(S(191,l));l.call(n)}}}var Sn={},Ge=Pt(Sn),an=Pt(Sn),on=Pt(Sn);function At(e){if(e===Sn)throw Error(S(174));return e}function oi(e,t){switch($(on,t),$(an,e),$(Ge,Sn),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Wl(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Wl(t,e)}B(Ge),$(Ge,t)}function Nr(){B(Ge),B(an),B(on)}function ku(e){At(on.current);var t=At(Ge.current),r=Wl(t,e.type);t!==r&&($(an,e),$(Ge,r))}function ci(e){an.current===e&&(B(Ge),B(an))}var Q=Pt(0);function Ns(e){for(var t=e;t!==null;){if(t.tag===13){var r=t.memoizedState;if(r!==null&&(r=r.dehydrated,r===null||r.data==="$?"||r.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var wl=[];function ui(){for(var e=0;e<wl.length;e++)wl[e]._workInProgressVersionPrimary=null;wl.length=0}var Zn=at.ReactCurrentDispatcher,Sl=at.ReactCurrentBatchConfig,Ut=0,Y=null,te=null,ne=null,ws=!1,Hr=!1,cn=0,Hf=0;function oe(){throw Error(S(321))}function di(e,t){if(t===null)return!1;for(var r=0;r<t.length&&r<e.length;r++)if(!Be(e[r],t[r]))return!1;return!0}function mi(e,t,r,n,l,a){if(Ut=a,Y=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Zn.current=e===null||e.memoizedState===null?Gf:Kf,e=r(n,l),Hr){a=0;do{if(Hr=!1,cn=0,25<=a)throw Error(S(301));a+=1,ne=te=null,t.updateQueue=null,Zn.current=qf,e=r(n,l)}while(Hr)}if(Zn.current=Ss,t=te!==null&&te.next!==null,Ut=0,ne=te=Y=null,ws=!1,t)throw Error(S(300));return e}function fi(){var e=cn!==0;return cn=0,e}function Qe(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return ne===null?Y.memoizedState=ne=e:ne=ne.next=e,ne}function ze(){if(te===null){var e=Y.alternate;e=e!==null?e.memoizedState:null}else e=te.next;var t=ne===null?Y.memoizedState:ne.next;if(t!==null)ne=t,te=e;else{if(e===null)throw Error(S(310));te=e,e={memoizedState:te.memoizedState,baseState:te.baseState,baseQueue:te.baseQueue,queue:te.queue,next:null},ne===null?Y.memoizedState=ne=e:ne=ne.next=e}return ne}function un(e,t){return typeof t=="function"?t(e):t}function kl(e){var t=ze(),r=t.queue;if(r===null)throw Error(S(311));r.lastRenderedReducer=e;var n=te,l=n.baseQueue,a=r.pending;if(a!==null){if(l!==null){var i=l.next;l.next=a.next,a.next=i}n.baseQueue=l=a,r.pending=null}if(l!==null){a=l.next,n=n.baseState;var o=i=null,c=null,u=a;do{var f=u.lane;if((Ut&f)===f)c!==null&&(c=c.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),n=u.hasEagerState?u.eagerState:e(n,u.action);else{var m={lane:f,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};c===null?(o=c=m,i=n):c=c.next=m,Y.lanes|=f,Bt|=f}u=u.next}while(u!==null&&u!==a);c===null?i=n:c.next=o,Be(n,t.memoizedState)||(je=!0),t.memoizedState=n,t.baseState=i,t.baseQueue=c,r.lastRenderedState=n}if(e=r.interleaved,e!==null){l=e;do a=l.lane,Y.lanes|=a,Bt|=a,l=l.next;while(l!==e)}else l===null&&(r.lanes=0);return[t.memoizedState,r.dispatch]}function Cl(e){var t=ze(),r=t.queue;if(r===null)throw Error(S(311));r.lastRenderedReducer=e;var n=r.dispatch,l=r.pending,a=t.memoizedState;if(l!==null){r.pending=null;var i=l=l.next;do a=e(a,i.action),i=i.next;while(i!==l);Be(a,t.memoizedState)||(je=!0),t.memoizedState=a,t.baseQueue===null&&(t.baseState=a),r.lastRenderedState=a}return[a,n]}function Cu(){}function bu(e,t){var r=Y,n=ze(),l=t(),a=!Be(n.memoizedState,l);if(a&&(n.memoizedState=l,je=!0),n=n.queue,pi(_u.bind(null,r,n,e),[e]),n.getSnapshot!==t||a||ne!==null&&ne.memoizedState.tag&1){if(r.flags|=2048,dn(9,Pu.bind(null,r,n,l,t),void 0,null),se===null)throw Error(S(349));Ut&30||Eu(r,t,l)}return l}function Eu(e,t,r){e.flags|=16384,e={getSnapshot:t,value:r},t=Y.updateQueue,t===null?(t={lastEffect:null,stores:null},Y.updateQueue=t,t.stores=[e]):(r=t.stores,r===null?t.stores=[e]:r.push(e))}function Pu(e,t,r,n){t.value=r,t.getSnapshot=n,Tu(t)&&Ru(e)}function _u(e,t,r){return r(function(){Tu(t)&&Ru(e)})}function Tu(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!Be(e,r)}catch{return!0}}function Ru(e){var t=st(e,1);t!==null&&Ue(t,e,1,-1)}function So(e){var t=Qe();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:un,lastRenderedState:e},t.queue=e,e=e.dispatch=Jf.bind(null,Y,e),[t.memoizedState,e]}function dn(e,t,r,n){return e={tag:e,create:t,destroy:r,deps:n,next:null},t=Y.updateQueue,t===null?(t={lastEffect:null,stores:null},Y.updateQueue=t,t.lastEffect=e.next=e):(r=t.lastEffect,r===null?t.lastEffect=e.next=e:(n=r.next,r.next=e,e.next=n,t.lastEffect=e)),e}function Ou(){return ze().memoizedState}function es(e,t,r,n){var l=Qe();Y.flags|=e,l.memoizedState=dn(1|t,r,void 0,n===void 0?null:n)}function Vs(e,t,r,n){var l=ze();n=n===void 0?null:n;var a=void 0;if(te!==null){var i=te.memoizedState;if(a=i.destroy,n!==null&&di(n,i.deps)){l.memoizedState=dn(t,r,a,n);return}}Y.flags|=e,l.memoizedState=dn(1|t,r,a,n)}function ko(e,t){return es(8390656,8,e,t)}function pi(e,t){return Vs(2048,8,e,t)}function Lu(e,t){return Vs(4,2,e,t)}function Mu(e,t){return Vs(4,4,e,t)}function zu(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Au(e,t,r){return r=r!=null?r.concat([e]):null,Vs(4,4,zu.bind(null,t,e),r)}function hi(){}function Iu(e,t){var r=ze();t=t===void 0?null:t;var n=r.memoizedState;return n!==null&&t!==null&&di(t,n[1])?n[0]:(r.memoizedState=[e,t],e)}function Du(e,t){var r=ze();t=t===void 0?null:t;var n=r.memoizedState;return n!==null&&t!==null&&di(t,n[1])?n[0]:(e=e(),r.memoizedState=[e,t],e)}function Fu(e,t,r){return Ut&21?(Be(r,t)||(r=Wc(),Y.lanes|=r,Bt|=r,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,je=!0),e.memoizedState=r)}function Qf(e,t){var r=D;D=r!==0&&4>r?r:4,e(!0);var n=Sl.transition;Sl.transition={};try{e(!1),t()}finally{D=r,Sl.transition=n}}function $u(){return ze().memoizedState}function Yf(e,t,r){var n=wt(e);if(r={lane:n,action:r,hasEagerState:!1,eagerState:null,next:null},Uu(e))Bu(t,r);else if(r=wu(e,t,r,n),r!==null){var l=fe();Ue(r,e,n,l),Vu(r,t,n)}}function Jf(e,t,r){var n=wt(e),l={lane:n,action:r,hasEagerState:!1,eagerState:null,next:null};if(Uu(e))Bu(t,l);else{var a=e.alternate;if(e.lanes===0&&(a===null||a.lanes===0)&&(a=t.lastRenderedReducer,a!==null))try{var i=t.lastRenderedState,o=a(i,r);if(l.hasEagerState=!0,l.eagerState=o,Be(o,i)){var c=t.interleaved;c===null?(l.next=l,ai(t)):(l.next=c.next,c.next=l),t.interleaved=l;return}}catch{}finally{}r=wu(e,t,l,n),r!==null&&(l=fe(),Ue(r,e,n,l),Vu(r,t,n))}}function Uu(e){var t=e.alternate;return e===Y||t!==null&&t===Y}function Bu(e,t){Hr=ws=!0;var r=e.pending;r===null?t.next=t:(t.next=r.next,r.next=t),e.pending=t}function Vu(e,t,r){if(r&4194240){var n=t.lanes;n&=e.pendingLanes,r|=n,t.lanes=r,Qa(e,r)}}var Ss={readContext:Me,useCallback:oe,useContext:oe,useEffect:oe,useImperativeHandle:oe,useInsertionEffect:oe,useLayoutEffect:oe,useMemo:oe,useReducer:oe,useRef:oe,useState:oe,useDebugValue:oe,useDeferredValue:oe,useTransition:oe,useMutableSource:oe,useSyncExternalStore:oe,useId:oe,unstable_isNewReconciler:!1},Gf={readContext:Me,useCallback:function(e,t){return Qe().memoizedState=[e,t===void 0?null:t],e},useContext:Me,useEffect:ko,useImperativeHandle:function(e,t,r){return r=r!=null?r.concat([e]):null,es(4194308,4,zu.bind(null,t,e),r)},useLayoutEffect:function(e,t){return es(4194308,4,e,t)},useInsertionEffect:function(e,t){return es(4,2,e,t)},useMemo:function(e,t){var r=Qe();return t=t===void 0?null:t,e=e(),r.memoizedState=[e,t],e},useReducer:function(e,t,r){var n=Qe();return t=r!==void 0?r(t):t,n.memoizedState=n.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},n.queue=e,e=e.dispatch=Yf.bind(null,Y,e),[n.memoizedState,e]},useRef:function(e){var t=Qe();return e={current:e},t.memoizedState=e},useState:So,useDebugValue:hi,useDeferredValue:function(e){return Qe().memoizedState=e},useTransition:function(){var e=So(!1),t=e[0];return e=Qf.bind(null,e[1]),Qe().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,r){var n=Y,l=Qe();if(W){if(r===void 0)throw Error(S(407));r=r()}else{if(r=t(),se===null)throw Error(S(349));Ut&30||Eu(n,t,r)}l.memoizedState=r;var a={value:r,getSnapshot:t};return l.queue=a,ko(_u.bind(null,n,a,e),[e]),n.flags|=2048,dn(9,Pu.bind(null,n,a,r,t),void 0,null),r},useId:function(){var e=Qe(),t=se.identifierPrefix;if(W){var r=et,n=Ze;r=(n&~(1<<32-$e(n)-1)).toString(32)+r,t=":"+t+"R"+r,r=cn++,0<r&&(t+="H"+r.toString(32)),t+=":"}else r=Hf++,t=":"+t+"r"+r.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},Kf={readContext:Me,useCallback:Iu,useContext:Me,useEffect:pi,useImperativeHandle:Au,useInsertionEffect:Lu,useLayoutEffect:Mu,useMemo:Du,useReducer:kl,useRef:Ou,useState:function(){return kl(un)},useDebugValue:hi,useDeferredValue:function(e){var t=ze();return Fu(t,te.memoizedState,e)},useTransition:function(){var e=kl(un)[0],t=ze().memoizedState;return[e,t]},useMutableSource:Cu,useSyncExternalStore:bu,useId:$u,unstable_isNewReconciler:!1},qf={readContext:Me,useCallback:Iu,useContext:Me,useEffect:pi,useImperativeHandle:Au,useInsertionEffect:Lu,useLayoutEffect:Mu,useMemo:Du,useReducer:Cl,useRef:Ou,useState:function(){return Cl(un)},useDebugValue:hi,useDeferredValue:function(e){var t=ze();return te===null?t.memoizedState=e:Fu(t,te.memoizedState,e)},useTransition:function(){var e=Cl(un)[0],t=ze().memoizedState;return[e,t]},useMutableSource:Cu,useSyncExternalStore:bu,useId:$u,unstable_isNewReconciler:!1};function Ie(e,t){if(e&&e.defaultProps){t=J({},t),e=e.defaultProps;for(var r in e)t[r]===void 0&&(t[r]=e[r]);return t}return t}function da(e,t,r,n){t=e.memoizedState,r=r(n,t),r=r==null?t:J({},t,r),e.memoizedState=r,e.lanes===0&&(e.updateQueue.baseState=r)}var Ws={isMounted:function(e){return(e=e._reactInternals)?Qt(e)===e:!1},enqueueSetState:function(e,t,r){e=e._reactInternals;var n=fe(),l=wt(e),a=tt(n,l);a.payload=t,r!=null&&(a.callback=r),t=jt(e,a,l),t!==null&&(Ue(t,e,l,n),Xn(t,e,l))},enqueueReplaceState:function(e,t,r){e=e._reactInternals;var n=fe(),l=wt(e),a=tt(n,l);a.tag=1,a.payload=t,r!=null&&(a.callback=r),t=jt(e,a,l),t!==null&&(Ue(t,e,l,n),Xn(t,e,l))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var r=fe(),n=wt(e),l=tt(r,n);l.tag=2,t!=null&&(l.callback=t),t=jt(e,l,n),t!==null&&(Ue(t,e,n,r),Xn(t,e,n))}};function Co(e,t,r,n,l,a,i){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(n,a,i):t.prototype&&t.prototype.isPureReactComponent?!rn(r,n)||!rn(l,a):!0}function Wu(e,t,r){var n=!1,l=bt,a=t.contextType;return typeof a=="object"&&a!==null?a=Me(a):(l=we(t)?Ft:de.current,n=t.contextTypes,a=(n=n!=null)?yr(e,l):bt),t=new t(r,a),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Ws,e.stateNode=t,t._reactInternals=e,n&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=l,e.__reactInternalMemoizedMaskedChildContext=a),t}function bo(e,t,r,n){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(r,n),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(r,n),t.state!==e&&Ws.enqueueReplaceState(t,t.state,null)}function ma(e,t,r,n){var l=e.stateNode;l.props=r,l.state=e.memoizedState,l.refs={},ii(e);var a=t.contextType;typeof a=="object"&&a!==null?l.context=Me(a):(a=we(t)?Ft:de.current,l.context=yr(e,a)),l.state=e.memoizedState,a=t.getDerivedStateFromProps,typeof a=="function"&&(da(e,t,a,r),l.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof l.getSnapshotBeforeUpdate=="function"||typeof l.UNSAFE_componentWillMount!="function"&&typeof l.componentWillMount!="function"||(t=l.state,typeof l.componentWillMount=="function"&&l.componentWillMount(),typeof l.UNSAFE_componentWillMount=="function"&&l.UNSAFE_componentWillMount(),t!==l.state&&Ws.enqueueReplaceState(l,l.state,null),js(e,r,l,n),l.state=e.memoizedState),typeof l.componentDidMount=="function"&&(e.flags|=4194308)}function wr(e,t){try{var r="",n=t;do r+=km(n),n=n.return;while(n);var l=r}catch(a){l=`
Error generating stack: `+a.message+`
`+a.stack}return{value:e,source:t,stack:l,digest:null}}function bl(e,t,r){return{value:e,source:null,stack:r??null,digest:t??null}}function fa(e,t){try{console.error(t.value)}catch(r){setTimeout(function(){throw r})}}var Xf=typeof WeakMap=="function"?WeakMap:Map;function Hu(e,t,r){r=tt(-1,r),r.tag=3,r.payload={element:null};var n=t.value;return r.callback=function(){Cs||(Cs=!0,Sa=n),fa(e,t)},r}function Qu(e,t,r){r=tt(-1,r),r.tag=3;var n=e.type.getDerivedStateFromError;if(typeof n=="function"){var l=t.value;r.payload=function(){return n(l)},r.callback=function(){fa(e,t)}}var a=e.stateNode;return a!==null&&typeof a.componentDidCatch=="function"&&(r.callback=function(){fa(e,t),typeof n!="function"&&(Nt===null?Nt=new Set([this]):Nt.add(this));var i=t.stack;this.componentDidCatch(t.value,{componentStack:i!==null?i:""})}),r}function Eo(e,t,r){var n=e.pingCache;if(n===null){n=e.pingCache=new Xf;var l=new Set;n.set(t,l)}else l=n.get(t),l===void 0&&(l=new Set,n.set(t,l));l.has(r)||(l.add(r),e=mp.bind(null,e,t,r),t.then(e,e))}function Po(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function _o(e,t,r,n,l){return e.mode&1?(e.flags|=65536,e.lanes=l,e):(e===t?e.flags|=65536:(e.flags|=128,r.flags|=131072,r.flags&=-52805,r.tag===1&&(r.alternate===null?r.tag=17:(t=tt(-1,1),t.tag=2,jt(r,t,1))),r.lanes|=1),e)}var Zf=at.ReactCurrentOwner,je=!1;function me(e,t,r,n){t.child=e===null?Nu(t,null,r,n):jr(t,e.child,r,n)}function To(e,t,r,n,l){r=r.render;var a=t.ref;return hr(t,l),n=mi(e,t,r,n,a,l),r=fi(),e!==null&&!je?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l,lt(e,t,l)):(W&&r&&ei(t),t.flags|=1,me(e,t,n,l),t.child)}function Ro(e,t,r,n,l){if(e===null){var a=r.type;return typeof a=="function"&&!Si(a)&&a.defaultProps===void 0&&r.compare===null&&r.defaultProps===void 0?(t.tag=15,t.type=a,Yu(e,t,a,n,l)):(e=ss(r.type,null,n,t,t.mode,l),e.ref=t.ref,e.return=t,t.child=e)}if(a=e.child,!(e.lanes&l)){var i=a.memoizedProps;if(r=r.compare,r=r!==null?r:rn,r(i,n)&&e.ref===t.ref)return lt(e,t,l)}return t.flags|=1,e=St(a,n),e.ref=t.ref,e.return=t,t.child=e}function Yu(e,t,r,n,l){if(e!==null){var a=e.memoizedProps;if(rn(a,n)&&e.ref===t.ref)if(je=!1,t.pendingProps=n=a,(e.lanes&l)!==0)e.flags&131072&&(je=!0);else return t.lanes=e.lanes,lt(e,t,l)}return pa(e,t,r,n,l)}function Ju(e,t,r){var n=t.pendingProps,l=n.children,a=e!==null?e.memoizedState:null;if(n.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},$(ur,ke),ke|=r;else{if(!(r&1073741824))return e=a!==null?a.baseLanes|r:r,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,$(ur,ke),ke|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},n=a!==null?a.baseLanes:r,$(ur,ke),ke|=n}else a!==null?(n=a.baseLanes|r,t.memoizedState=null):n=r,$(ur,ke),ke|=n;return me(e,t,l,r),t.child}function Gu(e,t){var r=t.ref;(e===null&&r!==null||e!==null&&e.ref!==r)&&(t.flags|=512,t.flags|=2097152)}function pa(e,t,r,n,l){var a=we(r)?Ft:de.current;return a=yr(t,a),hr(t,l),r=mi(e,t,r,n,a,l),n=fi(),e!==null&&!je?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l,lt(e,t,l)):(W&&n&&ei(t),t.flags|=1,me(e,t,r,l),t.child)}function Oo(e,t,r,n,l){if(we(r)){var a=!0;hs(t)}else a=!1;if(hr(t,l),t.stateNode===null)ts(e,t),Wu(t,r,n),ma(t,r,n,l),n=!0;else if(e===null){var i=t.stateNode,o=t.memoizedProps;i.props=o;var c=i.context,u=r.contextType;typeof u=="object"&&u!==null?u=Me(u):(u=we(r)?Ft:de.current,u=yr(t,u));var f=r.getDerivedStateFromProps,m=typeof f=="function"||typeof i.getSnapshotBeforeUpdate=="function";m||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(o!==n||c!==u)&&bo(t,i,n,u),ut=!1;var h=t.memoizedState;i.state=h,js(t,n,i,l),c=t.memoizedState,o!==n||h!==c||Ne.current||ut?(typeof f=="function"&&(da(t,r,f,n),c=t.memoizedState),(o=ut||Co(t,r,o,n,h,c,u))?(m||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount()),typeof i.componentDidMount=="function"&&(t.flags|=4194308)):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=n,t.memoizedState=c),i.props=n,i.state=c,i.context=u,n=o):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),n=!1)}else{i=t.stateNode,Su(e,t),o=t.memoizedProps,u=t.type===t.elementType?o:Ie(t.type,o),i.props=u,m=t.pendingProps,h=i.context,c=r.contextType,typeof c=="object"&&c!==null?c=Me(c):(c=we(r)?Ft:de.current,c=yr(t,c));var g=r.getDerivedStateFromProps;(f=typeof g=="function"||typeof i.getSnapshotBeforeUpdate=="function")||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(o!==m||h!==c)&&bo(t,i,n,c),ut=!1,h=t.memoizedState,i.state=h,js(t,n,i,l);var j=t.memoizedState;o!==m||h!==j||Ne.current||ut?(typeof g=="function"&&(da(t,r,g,n),j=t.memoizedState),(u=ut||Co(t,r,u,n,h,j,c)||!1)?(f||typeof i.UNSAFE_componentWillUpdate!="function"&&typeof i.componentWillUpdate!="function"||(typeof i.componentWillUpdate=="function"&&i.componentWillUpdate(n,j,c),typeof i.UNSAFE_componentWillUpdate=="function"&&i.UNSAFE_componentWillUpdate(n,j,c)),typeof i.componentDidUpdate=="function"&&(t.flags|=4),typeof i.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof i.componentDidUpdate!="function"||o===e.memoizedProps&&h===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||o===e.memoizedProps&&h===e.memoizedState||(t.flags|=1024),t.memoizedProps=n,t.memoizedState=j),i.props=n,i.state=j,i.context=c,n=u):(typeof i.componentDidUpdate!="function"||o===e.memoizedProps&&h===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||o===e.memoizedProps&&h===e.memoizedState||(t.flags|=1024),n=!1)}return ha(e,t,r,n,a,l)}function ha(e,t,r,n,l,a){Gu(e,t);var i=(t.flags&128)!==0;if(!n&&!i)return l&&go(t,r,!1),lt(e,t,a);n=t.stateNode,Zf.current=t;var o=i&&typeof r.getDerivedStateFromError!="function"?null:n.render();return t.flags|=1,e!==null&&i?(t.child=jr(t,e.child,null,a),t.child=jr(t,null,o,a)):me(e,t,o,a),t.memoizedState=n.state,l&&go(t,r,!0),t.child}function Ku(e){var t=e.stateNode;t.pendingContext?xo(e,t.pendingContext,t.pendingContext!==t.context):t.context&&xo(e,t.context,!1),oi(e,t.containerInfo)}function Lo(e,t,r,n,l){return vr(),ri(l),t.flags|=256,me(e,t,r,n),t.child}var xa={dehydrated:null,treeContext:null,retryLane:0};function ga(e){return{baseLanes:e,cachePool:null,transitions:null}}function qu(e,t,r){var n=t.pendingProps,l=Q.current,a=!1,i=(t.flags&128)!==0,o;if((o=i)||(o=e!==null&&e.memoizedState===null?!1:(l&2)!==0),o?(a=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(l|=1),$(Q,l&1),e===null)return ca(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(i=n.children,e=n.fallback,a?(n=t.mode,a=t.child,i={mode:"hidden",children:i},!(n&1)&&a!==null?(a.childLanes=0,a.pendingProps=i):a=Ys(i,n,0,null),e=Dt(e,n,r,null),a.return=t,e.return=t,a.sibling=e,t.child=a,t.child.memoizedState=ga(r),t.memoizedState=xa,e):xi(t,i));if(l=e.memoizedState,l!==null&&(o=l.dehydrated,o!==null))return ep(e,t,i,n,o,l,r);if(a){a=n.fallback,i=t.mode,l=e.child,o=l.sibling;var c={mode:"hidden",children:n.children};return!(i&1)&&t.child!==l?(n=t.child,n.childLanes=0,n.pendingProps=c,t.deletions=null):(n=St(l,c),n.subtreeFlags=l.subtreeFlags&14680064),o!==null?a=St(o,a):(a=Dt(a,i,r,null),a.flags|=2),a.return=t,n.return=t,n.sibling=a,t.child=n,n=a,a=t.child,i=e.child.memoizedState,i=i===null?ga(r):{baseLanes:i.baseLanes|r,cachePool:null,transitions:i.transitions},a.memoizedState=i,a.childLanes=e.childLanes&~r,t.memoizedState=xa,n}return a=e.child,e=a.sibling,n=St(a,{mode:"visible",children:n.children}),!(t.mode&1)&&(n.lanes=r),n.return=t,n.sibling=null,e!==null&&(r=t.deletions,r===null?(t.deletions=[e],t.flags|=16):r.push(e)),t.child=n,t.memoizedState=null,n}function xi(e,t){return t=Ys({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Vn(e,t,r,n){return n!==null&&ri(n),jr(t,e.child,null,r),e=xi(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function ep(e,t,r,n,l,a,i){if(r)return t.flags&256?(t.flags&=-257,n=bl(Error(S(422))),Vn(e,t,i,n)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(a=n.fallback,l=t.mode,n=Ys({mode:"visible",children:n.children},l,0,null),a=Dt(a,l,i,null),a.flags|=2,n.return=t,a.return=t,n.sibling=a,t.child=n,t.mode&1&&jr(t,e.child,null,i),t.child.memoizedState=ga(i),t.memoizedState=xa,a);if(!(t.mode&1))return Vn(e,t,i,null);if(l.data==="$!"){if(n=l.nextSibling&&l.nextSibling.dataset,n)var o=n.dgst;return n=o,a=Error(S(419)),n=bl(a,n,void 0),Vn(e,t,i,n)}if(o=(i&e.childLanes)!==0,je||o){if(n=se,n!==null){switch(i&-i){case 4:l=2;break;case 16:l=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:l=32;break;case 536870912:l=268435456;break;default:l=0}l=l&(n.suspendedLanes|i)?0:l,l!==0&&l!==a.retryLane&&(a.retryLane=l,st(e,l),Ue(n,e,l,-1))}return wi(),n=bl(Error(S(421))),Vn(e,t,i,n)}return l.data==="$?"?(t.flags|=128,t.child=e.child,t=fp.bind(null,e),l._reactRetry=t,null):(e=a.treeContext,Ce=vt(l.nextSibling),be=t,W=!0,Fe=null,e!==null&&(Te[Re++]=Ze,Te[Re++]=et,Te[Re++]=$t,Ze=e.id,et=e.overflow,$t=t),t=xi(t,n.children),t.flags|=4096,t)}function Mo(e,t,r){e.lanes|=t;var n=e.alternate;n!==null&&(n.lanes|=t),ua(e.return,t,r)}function El(e,t,r,n,l){var a=e.memoizedState;a===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:n,tail:r,tailMode:l}:(a.isBackwards=t,a.rendering=null,a.renderingStartTime=0,a.last=n,a.tail=r,a.tailMode=l)}function Xu(e,t,r){var n=t.pendingProps,l=n.revealOrder,a=n.tail;if(me(e,t,n.children,r),n=Q.current,n&2)n=n&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Mo(e,r,t);else if(e.tag===19)Mo(e,r,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}n&=1}if($(Q,n),!(t.mode&1))t.memoizedState=null;else switch(l){case"forwards":for(r=t.child,l=null;r!==null;)e=r.alternate,e!==null&&Ns(e)===null&&(l=r),r=r.sibling;r=l,r===null?(l=t.child,t.child=null):(l=r.sibling,r.sibling=null),El(t,!1,l,r,a);break;case"backwards":for(r=null,l=t.child,t.child=null;l!==null;){if(e=l.alternate,e!==null&&Ns(e)===null){t.child=l;break}e=l.sibling,l.sibling=r,r=l,l=e}El(t,!0,r,null,a);break;case"together":El(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function ts(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function lt(e,t,r){if(e!==null&&(t.dependencies=e.dependencies),Bt|=t.lanes,!(r&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(S(153));if(t.child!==null){for(e=t.child,r=St(e,e.pendingProps),t.child=r,r.return=t;e.sibling!==null;)e=e.sibling,r=r.sibling=St(e,e.pendingProps),r.return=t;r.sibling=null}return t.child}function tp(e,t,r){switch(t.tag){case 3:Ku(t),vr();break;case 5:ku(t);break;case 1:we(t.type)&&hs(t);break;case 4:oi(t,t.stateNode.containerInfo);break;case 10:var n=t.type._context,l=t.memoizedProps.value;$(ys,n._currentValue),n._currentValue=l;break;case 13:if(n=t.memoizedState,n!==null)return n.dehydrated!==null?($(Q,Q.current&1),t.flags|=128,null):r&t.child.childLanes?qu(e,t,r):($(Q,Q.current&1),e=lt(e,t,r),e!==null?e.sibling:null);$(Q,Q.current&1);break;case 19:if(n=(r&t.childLanes)!==0,e.flags&128){if(n)return Xu(e,t,r);t.flags|=128}if(l=t.memoizedState,l!==null&&(l.rendering=null,l.tail=null,l.lastEffect=null),$(Q,Q.current),n)break;return null;case 22:case 23:return t.lanes=0,Ju(e,t,r)}return lt(e,t,r)}var Zu,ya,ed,td;Zu=function(e,t){for(var r=t.child;r!==null;){if(r.tag===5||r.tag===6)e.appendChild(r.stateNode);else if(r.tag!==4&&r.child!==null){r.child.return=r,r=r.child;continue}if(r===t)break;for(;r.sibling===null;){if(r.return===null||r.return===t)return;r=r.return}r.sibling.return=r.return,r=r.sibling}};ya=function(){};ed=function(e,t,r,n){var l=e.memoizedProps;if(l!==n){e=t.stateNode,At(Ge.current);var a=null;switch(r){case"input":l=$l(e,l),n=$l(e,n),a=[];break;case"select":l=J({},l,{value:void 0}),n=J({},n,{value:void 0}),a=[];break;case"textarea":l=Vl(e,l),n=Vl(e,n),a=[];break;default:typeof l.onClick!="function"&&typeof n.onClick=="function"&&(e.onclick=fs)}Hl(r,n);var i;r=null;for(u in l)if(!n.hasOwnProperty(u)&&l.hasOwnProperty(u)&&l[u]!=null)if(u==="style"){var o=l[u];for(i in o)o.hasOwnProperty(i)&&(r||(r={}),r[i]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(Gr.hasOwnProperty(u)?a||(a=[]):(a=a||[]).push(u,null));for(u in n){var c=n[u];if(o=l!=null?l[u]:void 0,n.hasOwnProperty(u)&&c!==o&&(c!=null||o!=null))if(u==="style")if(o){for(i in o)!o.hasOwnProperty(i)||c&&c.hasOwnProperty(i)||(r||(r={}),r[i]="");for(i in c)c.hasOwnProperty(i)&&o[i]!==c[i]&&(r||(r={}),r[i]=c[i])}else r||(a||(a=[]),a.push(u,r)),r=c;else u==="dangerouslySetInnerHTML"?(c=c?c.__html:void 0,o=o?o.__html:void 0,c!=null&&o!==c&&(a=a||[]).push(u,c)):u==="children"?typeof c!="string"&&typeof c!="number"||(a=a||[]).push(u,""+c):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(Gr.hasOwnProperty(u)?(c!=null&&u==="onScroll"&&U("scroll",e),a||o===c||(a=[])):(a=a||[]).push(u,c))}r&&(a=a||[]).push("style",r);var u=a;(t.updateQueue=u)&&(t.flags|=4)}};td=function(e,t,r,n){r!==n&&(t.flags|=4)};function Mr(e,t){if(!W)switch(e.tailMode){case"hidden":t=e.tail;for(var r=null;t!==null;)t.alternate!==null&&(r=t),t=t.sibling;r===null?e.tail=null:r.sibling=null;break;case"collapsed":r=e.tail;for(var n=null;r!==null;)r.alternate!==null&&(n=r),r=r.sibling;n===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:n.sibling=null}}function ce(e){var t=e.alternate!==null&&e.alternate.child===e.child,r=0,n=0;if(t)for(var l=e.child;l!==null;)r|=l.lanes|l.childLanes,n|=l.subtreeFlags&14680064,n|=l.flags&14680064,l.return=e,l=l.sibling;else for(l=e.child;l!==null;)r|=l.lanes|l.childLanes,n|=l.subtreeFlags,n|=l.flags,l.return=e,l=l.sibling;return e.subtreeFlags|=n,e.childLanes=r,t}function rp(e,t,r){var n=t.pendingProps;switch(ti(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return ce(t),null;case 1:return we(t.type)&&ps(),ce(t),null;case 3:return n=t.stateNode,Nr(),B(Ne),B(de),ui(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),(e===null||e.child===null)&&(Un(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Fe!==null&&(ba(Fe),Fe=null))),ya(e,t),ce(t),null;case 5:ci(t);var l=At(on.current);if(r=t.type,e!==null&&t.stateNode!=null)ed(e,t,r,n,l),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!n){if(t.stateNode===null)throw Error(S(166));return ce(t),null}if(e=At(Ge.current),Un(t)){n=t.stateNode,r=t.type;var a=t.memoizedProps;switch(n[Ye]=t,n[ln]=a,e=(t.mode&1)!==0,r){case"dialog":U("cancel",n),U("close",n);break;case"iframe":case"object":case"embed":U("load",n);break;case"video":case"audio":for(l=0;l<Fr.length;l++)U(Fr[l],n);break;case"source":U("error",n);break;case"img":case"image":case"link":U("error",n),U("load",n);break;case"details":U("toggle",n);break;case"input":Bi(n,a),U("invalid",n);break;case"select":n._wrapperState={wasMultiple:!!a.multiple},U("invalid",n);break;case"textarea":Wi(n,a),U("invalid",n)}Hl(r,a),l=null;for(var i in a)if(a.hasOwnProperty(i)){var o=a[i];i==="children"?typeof o=="string"?n.textContent!==o&&(a.suppressHydrationWarning!==!0&&$n(n.textContent,o,e),l=["children",o]):typeof o=="number"&&n.textContent!==""+o&&(a.suppressHydrationWarning!==!0&&$n(n.textContent,o,e),l=["children",""+o]):Gr.hasOwnProperty(i)&&o!=null&&i==="onScroll"&&U("scroll",n)}switch(r){case"input":On(n),Vi(n,a,!0);break;case"textarea":On(n),Hi(n);break;case"select":case"option":break;default:typeof a.onClick=="function"&&(n.onclick=fs)}n=l,t.updateQueue=n,n!==null&&(t.flags|=4)}else{i=l.nodeType===9?l:l.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=_c(r)),e==="http://www.w3.org/1999/xhtml"?r==="script"?(e=i.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof n.is=="string"?e=i.createElement(r,{is:n.is}):(e=i.createElement(r),r==="select"&&(i=e,n.multiple?i.multiple=!0:n.size&&(i.size=n.size))):e=i.createElementNS(e,r),e[Ye]=t,e[ln]=n,Zu(e,t,!1,!1),t.stateNode=e;e:{switch(i=Ql(r,n),r){case"dialog":U("cancel",e),U("close",e),l=n;break;case"iframe":case"object":case"embed":U("load",e),l=n;break;case"video":case"audio":for(l=0;l<Fr.length;l++)U(Fr[l],e);l=n;break;case"source":U("error",e),l=n;break;case"img":case"image":case"link":U("error",e),U("load",e),l=n;break;case"details":U("toggle",e),l=n;break;case"input":Bi(e,n),l=$l(e,n),U("invalid",e);break;case"option":l=n;break;case"select":e._wrapperState={wasMultiple:!!n.multiple},l=J({},n,{value:void 0}),U("invalid",e);break;case"textarea":Wi(e,n),l=Vl(e,n),U("invalid",e);break;default:l=n}Hl(r,l),o=l;for(a in o)if(o.hasOwnProperty(a)){var c=o[a];a==="style"?Oc(e,c):a==="dangerouslySetInnerHTML"?(c=c?c.__html:void 0,c!=null&&Tc(e,c)):a==="children"?typeof c=="string"?(r!=="textarea"||c!=="")&&Kr(e,c):typeof c=="number"&&Kr(e,""+c):a!=="suppressContentEditableWarning"&&a!=="suppressHydrationWarning"&&a!=="autoFocus"&&(Gr.hasOwnProperty(a)?c!=null&&a==="onScroll"&&U("scroll",e):c!=null&&$a(e,a,c,i))}switch(r){case"input":On(e),Vi(e,n,!1);break;case"textarea":On(e),Hi(e);break;case"option":n.value!=null&&e.setAttribute("value",""+Ct(n.value));break;case"select":e.multiple=!!n.multiple,a=n.value,a!=null?dr(e,!!n.multiple,a,!1):n.defaultValue!=null&&dr(e,!!n.multiple,n.defaultValue,!0);break;default:typeof l.onClick=="function"&&(e.onclick=fs)}switch(r){case"button":case"input":case"select":case"textarea":n=!!n.autoFocus;break e;case"img":n=!0;break e;default:n=!1}}n&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return ce(t),null;case 6:if(e&&t.stateNode!=null)td(e,t,e.memoizedProps,n);else{if(typeof n!="string"&&t.stateNode===null)throw Error(S(166));if(r=At(on.current),At(Ge.current),Un(t)){if(n=t.stateNode,r=t.memoizedProps,n[Ye]=t,(a=n.nodeValue!==r)&&(e=be,e!==null))switch(e.tag){case 3:$n(n.nodeValue,r,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&$n(n.nodeValue,r,(e.mode&1)!==0)}a&&(t.flags|=4)}else n=(r.nodeType===9?r:r.ownerDocument).createTextNode(n),n[Ye]=t,t.stateNode=n}return ce(t),null;case 13:if(B(Q),n=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(W&&Ce!==null&&t.mode&1&&!(t.flags&128))vu(),vr(),t.flags|=98560,a=!1;else if(a=Un(t),n!==null&&n.dehydrated!==null){if(e===null){if(!a)throw Error(S(318));if(a=t.memoizedState,a=a!==null?a.dehydrated:null,!a)throw Error(S(317));a[Ye]=t}else vr(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;ce(t),a=!1}else Fe!==null&&(ba(Fe),Fe=null),a=!0;if(!a)return t.flags&65536?t:null}return t.flags&128?(t.lanes=r,t):(n=n!==null,n!==(e!==null&&e.memoizedState!==null)&&n&&(t.child.flags|=8192,t.mode&1&&(e===null||Q.current&1?re===0&&(re=3):wi())),t.updateQueue!==null&&(t.flags|=4),ce(t),null);case 4:return Nr(),ya(e,t),e===null&&nn(t.stateNode.containerInfo),ce(t),null;case 10:return li(t.type._context),ce(t),null;case 17:return we(t.type)&&ps(),ce(t),null;case 19:if(B(Q),a=t.memoizedState,a===null)return ce(t),null;if(n=(t.flags&128)!==0,i=a.rendering,i===null)if(n)Mr(a,!1);else{if(re!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(i=Ns(e),i!==null){for(t.flags|=128,Mr(a,!1),n=i.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),t.subtreeFlags=0,n=r,r=t.child;r!==null;)a=r,e=n,a.flags&=14680066,i=a.alternate,i===null?(a.childLanes=0,a.lanes=e,a.child=null,a.subtreeFlags=0,a.memoizedProps=null,a.memoizedState=null,a.updateQueue=null,a.dependencies=null,a.stateNode=null):(a.childLanes=i.childLanes,a.lanes=i.lanes,a.child=i.child,a.subtreeFlags=0,a.deletions=null,a.memoizedProps=i.memoizedProps,a.memoizedState=i.memoizedState,a.updateQueue=i.updateQueue,a.type=i.type,e=i.dependencies,a.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),r=r.sibling;return $(Q,Q.current&1|2),t.child}e=e.sibling}a.tail!==null&&q()>Sr&&(t.flags|=128,n=!0,Mr(a,!1),t.lanes=4194304)}else{if(!n)if(e=Ns(i),e!==null){if(t.flags|=128,n=!0,r=e.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),Mr(a,!0),a.tail===null&&a.tailMode==="hidden"&&!i.alternate&&!W)return ce(t),null}else 2*q()-a.renderingStartTime>Sr&&r!==1073741824&&(t.flags|=128,n=!0,Mr(a,!1),t.lanes=4194304);a.isBackwards?(i.sibling=t.child,t.child=i):(r=a.last,r!==null?r.sibling=i:t.child=i,a.last=i)}return a.tail!==null?(t=a.tail,a.rendering=t,a.tail=t.sibling,a.renderingStartTime=q(),t.sibling=null,r=Q.current,$(Q,n?r&1|2:r&1),t):(ce(t),null);case 22:case 23:return Ni(),n=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==n&&(t.flags|=8192),n&&t.mode&1?ke&1073741824&&(ce(t),t.subtreeFlags&6&&(t.flags|=8192)):ce(t),null;case 24:return null;case 25:return null}throw Error(S(156,t.tag))}function np(e,t){switch(ti(t),t.tag){case 1:return we(t.type)&&ps(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Nr(),B(Ne),B(de),ui(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return ci(t),null;case 13:if(B(Q),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(S(340));vr()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return B(Q),null;case 4:return Nr(),null;case 10:return li(t.type._context),null;case 22:case 23:return Ni(),null;case 24:return null;default:return null}}var Wn=!1,ue=!1,sp=typeof WeakSet=="function"?WeakSet:Set,P=null;function cr(e,t){var r=e.ref;if(r!==null)if(typeof r=="function")try{r(null)}catch(n){K(e,t,n)}else r.current=null}function va(e,t,r){try{r()}catch(n){K(e,t,n)}}var zo=!1;function lp(e,t){if(ra=us,e=au(),Za(e)){if("selectionStart"in e)var r={start:e.selectionStart,end:e.selectionEnd};else e:{r=(r=e.ownerDocument)&&r.defaultView||window;var n=r.getSelection&&r.getSelection();if(n&&n.rangeCount!==0){r=n.anchorNode;var l=n.anchorOffset,a=n.focusNode;n=n.focusOffset;try{r.nodeType,a.nodeType}catch{r=null;break e}var i=0,o=-1,c=-1,u=0,f=0,m=e,h=null;t:for(;;){for(var g;m!==r||l!==0&&m.nodeType!==3||(o=i+l),m!==a||n!==0&&m.nodeType!==3||(c=i+n),m.nodeType===3&&(i+=m.nodeValue.length),(g=m.firstChild)!==null;)h=m,m=g;for(;;){if(m===e)break t;if(h===r&&++u===l&&(o=i),h===a&&++f===n&&(c=i),(g=m.nextSibling)!==null)break;m=h,h=m.parentNode}m=g}r=o===-1||c===-1?null:{start:o,end:c}}else r=null}r=r||{start:0,end:0}}else r=null;for(na={focusedElem:e,selectionRange:r},us=!1,P=t;P!==null;)if(t=P,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,P=e;else for(;P!==null;){t=P;try{var j=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(j!==null){var y=j.memoizedProps,w=j.memoizedState,p=t.stateNode,d=p.getSnapshotBeforeUpdate(t.elementType===t.type?y:Ie(t.type,y),w);p.__reactInternalSnapshotBeforeUpdate=d}break;case 3:var x=t.stateNode.containerInfo;x.nodeType===1?x.textContent="":x.nodeType===9&&x.documentElement&&x.removeChild(x.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(S(163))}}catch(v){K(t,t.return,v)}if(e=t.sibling,e!==null){e.return=t.return,P=e;break}P=t.return}return j=zo,zo=!1,j}function Qr(e,t,r){var n=t.updateQueue;if(n=n!==null?n.lastEffect:null,n!==null){var l=n=n.next;do{if((l.tag&e)===e){var a=l.destroy;l.destroy=void 0,a!==void 0&&va(t,r,a)}l=l.next}while(l!==n)}}function Hs(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var r=t=t.next;do{if((r.tag&e)===e){var n=r.create;r.destroy=n()}r=r.next}while(r!==t)}}function ja(e){var t=e.ref;if(t!==null){var r=e.stateNode;switch(e.tag){case 5:e=r;break;default:e=r}typeof t=="function"?t(e):t.current=e}}function rd(e){var t=e.alternate;t!==null&&(e.alternate=null,rd(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[Ye],delete t[ln],delete t[aa],delete t[Uf],delete t[Bf])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function nd(e){return e.tag===5||e.tag===3||e.tag===4}function Ao(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||nd(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Na(e,t,r){var n=e.tag;if(n===5||n===6)e=e.stateNode,t?r.nodeType===8?r.parentNode.insertBefore(e,t):r.insertBefore(e,t):(r.nodeType===8?(t=r.parentNode,t.insertBefore(e,r)):(t=r,t.appendChild(e)),r=r._reactRootContainer,r!=null||t.onclick!==null||(t.onclick=fs));else if(n!==4&&(e=e.child,e!==null))for(Na(e,t,r),e=e.sibling;e!==null;)Na(e,t,r),e=e.sibling}function wa(e,t,r){var n=e.tag;if(n===5||n===6)e=e.stateNode,t?r.insertBefore(e,t):r.appendChild(e);else if(n!==4&&(e=e.child,e!==null))for(wa(e,t,r),e=e.sibling;e!==null;)wa(e,t,r),e=e.sibling}var le=null,De=!1;function ot(e,t,r){for(r=r.child;r!==null;)sd(e,t,r),r=r.sibling}function sd(e,t,r){if(Je&&typeof Je.onCommitFiberUnmount=="function")try{Je.onCommitFiberUnmount(Is,r)}catch{}switch(r.tag){case 5:ue||cr(r,t);case 6:var n=le,l=De;le=null,ot(e,t,r),le=n,De=l,le!==null&&(De?(e=le,r=r.stateNode,e.nodeType===8?e.parentNode.removeChild(r):e.removeChild(r)):le.removeChild(r.stateNode));break;case 18:le!==null&&(De?(e=le,r=r.stateNode,e.nodeType===8?jl(e.parentNode,r):e.nodeType===1&&jl(e,r),en(e)):jl(le,r.stateNode));break;case 4:n=le,l=De,le=r.stateNode.containerInfo,De=!0,ot(e,t,r),le=n,De=l;break;case 0:case 11:case 14:case 15:if(!ue&&(n=r.updateQueue,n!==null&&(n=n.lastEffect,n!==null))){l=n=n.next;do{var a=l,i=a.destroy;a=a.tag,i!==void 0&&(a&2||a&4)&&va(r,t,i),l=l.next}while(l!==n)}ot(e,t,r);break;case 1:if(!ue&&(cr(r,t),n=r.stateNode,typeof n.componentWillUnmount=="function"))try{n.props=r.memoizedProps,n.state=r.memoizedState,n.componentWillUnmount()}catch(o){K(r,t,o)}ot(e,t,r);break;case 21:ot(e,t,r);break;case 22:r.mode&1?(ue=(n=ue)||r.memoizedState!==null,ot(e,t,r),ue=n):ot(e,t,r);break;default:ot(e,t,r)}}function Io(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var r=e.stateNode;r===null&&(r=e.stateNode=new sp),t.forEach(function(n){var l=pp.bind(null,e,n);r.has(n)||(r.add(n),n.then(l,l))})}}function Ae(e,t){var r=t.deletions;if(r!==null)for(var n=0;n<r.length;n++){var l=r[n];try{var a=e,i=t,o=i;e:for(;o!==null;){switch(o.tag){case 5:le=o.stateNode,De=!1;break e;case 3:le=o.stateNode.containerInfo,De=!0;break e;case 4:le=o.stateNode.containerInfo,De=!0;break e}o=o.return}if(le===null)throw Error(S(160));sd(a,i,l),le=null,De=!1;var c=l.alternate;c!==null&&(c.return=null),l.return=null}catch(u){K(l,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)ld(t,e),t=t.sibling}function ld(e,t){var r=e.alternate,n=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Ae(t,e),He(e),n&4){try{Qr(3,e,e.return),Hs(3,e)}catch(y){K(e,e.return,y)}try{Qr(5,e,e.return)}catch(y){K(e,e.return,y)}}break;case 1:Ae(t,e),He(e),n&512&&r!==null&&cr(r,r.return);break;case 5:if(Ae(t,e),He(e),n&512&&r!==null&&cr(r,r.return),e.flags&32){var l=e.stateNode;try{Kr(l,"")}catch(y){K(e,e.return,y)}}if(n&4&&(l=e.stateNode,l!=null)){var a=e.memoizedProps,i=r!==null?r.memoizedProps:a,o=e.type,c=e.updateQueue;if(e.updateQueue=null,c!==null)try{o==="input"&&a.type==="radio"&&a.name!=null&&Ec(l,a),Ql(o,i);var u=Ql(o,a);for(i=0;i<c.length;i+=2){var f=c[i],m=c[i+1];f==="style"?Oc(l,m):f==="dangerouslySetInnerHTML"?Tc(l,m):f==="children"?Kr(l,m):$a(l,f,m,u)}switch(o){case"input":Ul(l,a);break;case"textarea":Pc(l,a);break;case"select":var h=l._wrapperState.wasMultiple;l._wrapperState.wasMultiple=!!a.multiple;var g=a.value;g!=null?dr(l,!!a.multiple,g,!1):h!==!!a.multiple&&(a.defaultValue!=null?dr(l,!!a.multiple,a.defaultValue,!0):dr(l,!!a.multiple,a.multiple?[]:"",!1))}l[ln]=a}catch(y){K(e,e.return,y)}}break;case 6:if(Ae(t,e),He(e),n&4){if(e.stateNode===null)throw Error(S(162));l=e.stateNode,a=e.memoizedProps;try{l.nodeValue=a}catch(y){K(e,e.return,y)}}break;case 3:if(Ae(t,e),He(e),n&4&&r!==null&&r.memoizedState.isDehydrated)try{en(t.containerInfo)}catch(y){K(e,e.return,y)}break;case 4:Ae(t,e),He(e);break;case 13:Ae(t,e),He(e),l=e.child,l.flags&8192&&(a=l.memoizedState!==null,l.stateNode.isHidden=a,!a||l.alternate!==null&&l.alternate.memoizedState!==null||(vi=q())),n&4&&Io(e);break;case 22:if(f=r!==null&&r.memoizedState!==null,e.mode&1?(ue=(u=ue)||f,Ae(t,e),ue=u):Ae(t,e),He(e),n&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!f&&e.mode&1)for(P=e,f=e.child;f!==null;){for(m=P=f;P!==null;){switch(h=P,g=h.child,h.tag){case 0:case 11:case 14:case 15:Qr(4,h,h.return);break;case 1:cr(h,h.return);var j=h.stateNode;if(typeof j.componentWillUnmount=="function"){n=h,r=h.return;try{t=n,j.props=t.memoizedProps,j.state=t.memoizedState,j.componentWillUnmount()}catch(y){K(n,r,y)}}break;case 5:cr(h,h.return);break;case 22:if(h.memoizedState!==null){Fo(m);continue}}g!==null?(g.return=h,P=g):Fo(m)}f=f.sibling}e:for(f=null,m=e;;){if(m.tag===5){if(f===null){f=m;try{l=m.stateNode,u?(a=l.style,typeof a.setProperty=="function"?a.setProperty("display","none","important"):a.display="none"):(o=m.stateNode,c=m.memoizedProps.style,i=c!=null&&c.hasOwnProperty("display")?c.display:null,o.style.display=Rc("display",i))}catch(y){K(e,e.return,y)}}}else if(m.tag===6){if(f===null)try{m.stateNode.nodeValue=u?"":m.memoizedProps}catch(y){K(e,e.return,y)}}else if((m.tag!==22&&m.tag!==23||m.memoizedState===null||m===e)&&m.child!==null){m.child.return=m,m=m.child;continue}if(m===e)break e;for(;m.sibling===null;){if(m.return===null||m.return===e)break e;f===m&&(f=null),m=m.return}f===m&&(f=null),m.sibling.return=m.return,m=m.sibling}}break;case 19:Ae(t,e),He(e),n&4&&Io(e);break;case 21:break;default:Ae(t,e),He(e)}}function He(e){var t=e.flags;if(t&2){try{e:{for(var r=e.return;r!==null;){if(nd(r)){var n=r;break e}r=r.return}throw Error(S(160))}switch(n.tag){case 5:var l=n.stateNode;n.flags&32&&(Kr(l,""),n.flags&=-33);var a=Ao(e);wa(e,a,l);break;case 3:case 4:var i=n.stateNode.containerInfo,o=Ao(e);Na(e,o,i);break;default:throw Error(S(161))}}catch(c){K(e,e.return,c)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function ap(e,t,r){P=e,ad(e)}function ad(e,t,r){for(var n=(e.mode&1)!==0;P!==null;){var l=P,a=l.child;if(l.tag===22&&n){var i=l.memoizedState!==null||Wn;if(!i){var o=l.alternate,c=o!==null&&o.memoizedState!==null||ue;o=Wn;var u=ue;if(Wn=i,(ue=c)&&!u)for(P=l;P!==null;)i=P,c=i.child,i.tag===22&&i.memoizedState!==null?$o(l):c!==null?(c.return=i,P=c):$o(l);for(;a!==null;)P=a,ad(a),a=a.sibling;P=l,Wn=o,ue=u}Do(e)}else l.subtreeFlags&8772&&a!==null?(a.return=l,P=a):Do(e)}}function Do(e){for(;P!==null;){var t=P;if(t.flags&8772){var r=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:ue||Hs(5,t);break;case 1:var n=t.stateNode;if(t.flags&4&&!ue)if(r===null)n.componentDidMount();else{var l=t.elementType===t.type?r.memoizedProps:Ie(t.type,r.memoizedProps);n.componentDidUpdate(l,r.memoizedState,n.__reactInternalSnapshotBeforeUpdate)}var a=t.updateQueue;a!==null&&wo(t,a,n);break;case 3:var i=t.updateQueue;if(i!==null){if(r=null,t.child!==null)switch(t.child.tag){case 5:r=t.child.stateNode;break;case 1:r=t.child.stateNode}wo(t,i,r)}break;case 5:var o=t.stateNode;if(r===null&&t.flags&4){r=o;var c=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":c.autoFocus&&r.focus();break;case"img":c.src&&(r.src=c.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var f=u.memoizedState;if(f!==null){var m=f.dehydrated;m!==null&&en(m)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(S(163))}ue||t.flags&512&&ja(t)}catch(h){K(t,t.return,h)}}if(t===e){P=null;break}if(r=t.sibling,r!==null){r.return=t.return,P=r;break}P=t.return}}function Fo(e){for(;P!==null;){var t=P;if(t===e){P=null;break}var r=t.sibling;if(r!==null){r.return=t.return,P=r;break}P=t.return}}function $o(e){for(;P!==null;){var t=P;try{switch(t.tag){case 0:case 11:case 15:var r=t.return;try{Hs(4,t)}catch(c){K(t,r,c)}break;case 1:var n=t.stateNode;if(typeof n.componentDidMount=="function"){var l=t.return;try{n.componentDidMount()}catch(c){K(t,l,c)}}var a=t.return;try{ja(t)}catch(c){K(t,a,c)}break;case 5:var i=t.return;try{ja(t)}catch(c){K(t,i,c)}}}catch(c){K(t,t.return,c)}if(t===e){P=null;break}var o=t.sibling;if(o!==null){o.return=t.return,P=o;break}P=t.return}}var ip=Math.ceil,ks=at.ReactCurrentDispatcher,gi=at.ReactCurrentOwner,Le=at.ReactCurrentBatchConfig,I=0,se=null,Z=null,ae=0,ke=0,ur=Pt(0),re=0,mn=null,Bt=0,Qs=0,yi=0,Yr=null,ve=null,vi=0,Sr=1/0,qe=null,Cs=!1,Sa=null,Nt=null,Hn=!1,pt=null,bs=0,Jr=0,ka=null,rs=-1,ns=0;function fe(){return I&6?q():rs!==-1?rs:rs=q()}function wt(e){return e.mode&1?I&2&&ae!==0?ae&-ae:Wf.transition!==null?(ns===0&&(ns=Wc()),ns):(e=D,e!==0||(e=window.event,e=e===void 0?16:qc(e.type)),e):1}function Ue(e,t,r,n){if(50<Jr)throw Jr=0,ka=null,Error(S(185));jn(e,r,n),(!(I&2)||e!==se)&&(e===se&&(!(I&2)&&(Qs|=r),re===4&&mt(e,ae)),Se(e,n),r===1&&I===0&&!(t.mode&1)&&(Sr=q()+500,Bs&&_t()))}function Se(e,t){var r=e.callbackNode;Vm(e,t);var n=cs(e,e===se?ae:0);if(n===0)r!==null&&Ji(r),e.callbackNode=null,e.callbackPriority=0;else if(t=n&-n,e.callbackPriority!==t){if(r!=null&&Ji(r),t===1)e.tag===0?Vf(Uo.bind(null,e)):xu(Uo.bind(null,e)),Ff(function(){!(I&6)&&_t()}),r=null;else{switch(Hc(n)){case 1:r=Ha;break;case 4:r=Bc;break;case 16:r=os;break;case 536870912:r=Vc;break;default:r=os}r=pd(r,id.bind(null,e))}e.callbackPriority=t,e.callbackNode=r}}function id(e,t){if(rs=-1,ns=0,I&6)throw Error(S(327));var r=e.callbackNode;if(xr()&&e.callbackNode!==r)return null;var n=cs(e,e===se?ae:0);if(n===0)return null;if(n&30||n&e.expiredLanes||t)t=Es(e,n);else{t=n;var l=I;I|=2;var a=cd();(se!==e||ae!==t)&&(qe=null,Sr=q()+500,It(e,t));do try{up();break}catch(o){od(e,o)}while(1);si(),ks.current=a,I=l,Z!==null?t=0:(se=null,ae=0,t=re)}if(t!==0){if(t===2&&(l=ql(e),l!==0&&(n=l,t=Ca(e,l))),t===1)throw r=mn,It(e,0),mt(e,n),Se(e,q()),r;if(t===6)mt(e,n);else{if(l=e.current.alternate,!(n&30)&&!op(l)&&(t=Es(e,n),t===2&&(a=ql(e),a!==0&&(n=a,t=Ca(e,a))),t===1))throw r=mn,It(e,0),mt(e,n),Se(e,q()),r;switch(e.finishedWork=l,e.finishedLanes=n,t){case 0:case 1:throw Error(S(345));case 2:Lt(e,ve,qe);break;case 3:if(mt(e,n),(n&130023424)===n&&(t=vi+500-q(),10<t)){if(cs(e,0)!==0)break;if(l=e.suspendedLanes,(l&n)!==n){fe(),e.pingedLanes|=e.suspendedLanes&l;break}e.timeoutHandle=la(Lt.bind(null,e,ve,qe),t);break}Lt(e,ve,qe);break;case 4:if(mt(e,n),(n&4194240)===n)break;for(t=e.eventTimes,l=-1;0<n;){var i=31-$e(n);a=1<<i,i=t[i],i>l&&(l=i),n&=~a}if(n=l,n=q()-n,n=(120>n?120:480>n?480:1080>n?1080:1920>n?1920:3e3>n?3e3:4320>n?4320:1960*ip(n/1960))-n,10<n){e.timeoutHandle=la(Lt.bind(null,e,ve,qe),n);break}Lt(e,ve,qe);break;case 5:Lt(e,ve,qe);break;default:throw Error(S(329))}}}return Se(e,q()),e.callbackNode===r?id.bind(null,e):null}function Ca(e,t){var r=Yr;return e.current.memoizedState.isDehydrated&&(It(e,t).flags|=256),e=Es(e,t),e!==2&&(t=ve,ve=r,t!==null&&ba(t)),e}function ba(e){ve===null?ve=e:ve.push.apply(ve,e)}function op(e){for(var t=e;;){if(t.flags&16384){var r=t.updateQueue;if(r!==null&&(r=r.stores,r!==null))for(var n=0;n<r.length;n++){var l=r[n],a=l.getSnapshot;l=l.value;try{if(!Be(a(),l))return!1}catch{return!1}}}if(r=t.child,t.subtreeFlags&16384&&r!==null)r.return=t,t=r;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function mt(e,t){for(t&=~yi,t&=~Qs,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var r=31-$e(t),n=1<<r;e[r]=-1,t&=~n}}function Uo(e){if(I&6)throw Error(S(327));xr();var t=cs(e,0);if(!(t&1))return Se(e,q()),null;var r=Es(e,t);if(e.tag!==0&&r===2){var n=ql(e);n!==0&&(t=n,r=Ca(e,n))}if(r===1)throw r=mn,It(e,0),mt(e,t),Se(e,q()),r;if(r===6)throw Error(S(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Lt(e,ve,qe),Se(e,q()),null}function ji(e,t){var r=I;I|=1;try{return e(t)}finally{I=r,I===0&&(Sr=q()+500,Bs&&_t())}}function Vt(e){pt!==null&&pt.tag===0&&!(I&6)&&xr();var t=I;I|=1;var r=Le.transition,n=D;try{if(Le.transition=null,D=1,e)return e()}finally{D=n,Le.transition=r,I=t,!(I&6)&&_t()}}function Ni(){ke=ur.current,B(ur)}function It(e,t){e.finishedWork=null,e.finishedLanes=0;var r=e.timeoutHandle;if(r!==-1&&(e.timeoutHandle=-1,Df(r)),Z!==null)for(r=Z.return;r!==null;){var n=r;switch(ti(n),n.tag){case 1:n=n.type.childContextTypes,n!=null&&ps();break;case 3:Nr(),B(Ne),B(de),ui();break;case 5:ci(n);break;case 4:Nr();break;case 13:B(Q);break;case 19:B(Q);break;case 10:li(n.type._context);break;case 22:case 23:Ni()}r=r.return}if(se=e,Z=e=St(e.current,null),ae=ke=t,re=0,mn=null,yi=Qs=Bt=0,ve=Yr=null,zt!==null){for(t=0;t<zt.length;t++)if(r=zt[t],n=r.interleaved,n!==null){r.interleaved=null;var l=n.next,a=r.pending;if(a!==null){var i=a.next;a.next=l,n.next=i}r.pending=n}zt=null}return e}function od(e,t){do{var r=Z;try{if(si(),Zn.current=Ss,ws){for(var n=Y.memoizedState;n!==null;){var l=n.queue;l!==null&&(l.pending=null),n=n.next}ws=!1}if(Ut=0,ne=te=Y=null,Hr=!1,cn=0,gi.current=null,r===null||r.return===null){re=1,mn=t,Z=null;break}e:{var a=e,i=r.return,o=r,c=t;if(t=ae,o.flags|=32768,c!==null&&typeof c=="object"&&typeof c.then=="function"){var u=c,f=o,m=f.tag;if(!(f.mode&1)&&(m===0||m===11||m===15)){var h=f.alternate;h?(f.updateQueue=h.updateQueue,f.memoizedState=h.memoizedState,f.lanes=h.lanes):(f.updateQueue=null,f.memoizedState=null)}var g=Po(i);if(g!==null){g.flags&=-257,_o(g,i,o,a,t),g.mode&1&&Eo(a,u,t),t=g,c=u;var j=t.updateQueue;if(j===null){var y=new Set;y.add(c),t.updateQueue=y}else j.add(c);break e}else{if(!(t&1)){Eo(a,u,t),wi();break e}c=Error(S(426))}}else if(W&&o.mode&1){var w=Po(i);if(w!==null){!(w.flags&65536)&&(w.flags|=256),_o(w,i,o,a,t),ri(wr(c,o));break e}}a=c=wr(c,o),re!==4&&(re=2),Yr===null?Yr=[a]:Yr.push(a),a=i;do{switch(a.tag){case 3:a.flags|=65536,t&=-t,a.lanes|=t;var p=Hu(a,c,t);No(a,p);break e;case 1:o=c;var d=a.type,x=a.stateNode;if(!(a.flags&128)&&(typeof d.getDerivedStateFromError=="function"||x!==null&&typeof x.componentDidCatch=="function"&&(Nt===null||!Nt.has(x)))){a.flags|=65536,t&=-t,a.lanes|=t;var v=Qu(a,o,t);No(a,v);break e}}a=a.return}while(a!==null)}dd(r)}catch(k){t=k,Z===r&&r!==null&&(Z=r=r.return);continue}break}while(1)}function cd(){var e=ks.current;return ks.current=Ss,e===null?Ss:e}function wi(){(re===0||re===3||re===2)&&(re=4),se===null||!(Bt&268435455)&&!(Qs&268435455)||mt(se,ae)}function Es(e,t){var r=I;I|=2;var n=cd();(se!==e||ae!==t)&&(qe=null,It(e,t));do try{cp();break}catch(l){od(e,l)}while(1);if(si(),I=r,ks.current=n,Z!==null)throw Error(S(261));return se=null,ae=0,re}function cp(){for(;Z!==null;)ud(Z)}function up(){for(;Z!==null&&!Mm();)ud(Z)}function ud(e){var t=fd(e.alternate,e,ke);e.memoizedProps=e.pendingProps,t===null?dd(e):Z=t,gi.current=null}function dd(e){var t=e;do{var r=t.alternate;if(e=t.return,t.flags&32768){if(r=np(r,t),r!==null){r.flags&=32767,Z=r;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{re=6,Z=null;return}}else if(r=rp(r,t,ke),r!==null){Z=r;return}if(t=t.sibling,t!==null){Z=t;return}Z=t=e}while(t!==null);re===0&&(re=5)}function Lt(e,t,r){var n=D,l=Le.transition;try{Le.transition=null,D=1,dp(e,t,r,n)}finally{Le.transition=l,D=n}return null}function dp(e,t,r,n){do xr();while(pt!==null);if(I&6)throw Error(S(327));r=e.finishedWork;var l=e.finishedLanes;if(r===null)return null;if(e.finishedWork=null,e.finishedLanes=0,r===e.current)throw Error(S(177));e.callbackNode=null,e.callbackPriority=0;var a=r.lanes|r.childLanes;if(Wm(e,a),e===se&&(Z=se=null,ae=0),!(r.subtreeFlags&2064)&&!(r.flags&2064)||Hn||(Hn=!0,pd(os,function(){return xr(),null})),a=(r.flags&15990)!==0,r.subtreeFlags&15990||a){a=Le.transition,Le.transition=null;var i=D;D=1;var o=I;I|=4,gi.current=null,lp(e,r),ld(r,e),Rf(na),us=!!ra,na=ra=null,e.current=r,ap(r),zm(),I=o,D=i,Le.transition=a}else e.current=r;if(Hn&&(Hn=!1,pt=e,bs=l),a=e.pendingLanes,a===0&&(Nt=null),Dm(r.stateNode),Se(e,q()),t!==null)for(n=e.onRecoverableError,r=0;r<t.length;r++)l=t[r],n(l.value,{componentStack:l.stack,digest:l.digest});if(Cs)throw Cs=!1,e=Sa,Sa=null,e;return bs&1&&e.tag!==0&&xr(),a=e.pendingLanes,a&1?e===ka?Jr++:(Jr=0,ka=e):Jr=0,_t(),null}function xr(){if(pt!==null){var e=Hc(bs),t=Le.transition,r=D;try{if(Le.transition=null,D=16>e?16:e,pt===null)var n=!1;else{if(e=pt,pt=null,bs=0,I&6)throw Error(S(331));var l=I;for(I|=4,P=e.current;P!==null;){var a=P,i=a.child;if(P.flags&16){var o=a.deletions;if(o!==null){for(var c=0;c<o.length;c++){var u=o[c];for(P=u;P!==null;){var f=P;switch(f.tag){case 0:case 11:case 15:Qr(8,f,a)}var m=f.child;if(m!==null)m.return=f,P=m;else for(;P!==null;){f=P;var h=f.sibling,g=f.return;if(rd(f),f===u){P=null;break}if(h!==null){h.return=g,P=h;break}P=g}}}var j=a.alternate;if(j!==null){var y=j.child;if(y!==null){j.child=null;do{var w=y.sibling;y.sibling=null,y=w}while(y!==null)}}P=a}}if(a.subtreeFlags&2064&&i!==null)i.return=a,P=i;else e:for(;P!==null;){if(a=P,a.flags&2048)switch(a.tag){case 0:case 11:case 15:Qr(9,a,a.return)}var p=a.sibling;if(p!==null){p.return=a.return,P=p;break e}P=a.return}}var d=e.current;for(P=d;P!==null;){i=P;var x=i.child;if(i.subtreeFlags&2064&&x!==null)x.return=i,P=x;else e:for(i=d;P!==null;){if(o=P,o.flags&2048)try{switch(o.tag){case 0:case 11:case 15:Hs(9,o)}}catch(k){K(o,o.return,k)}if(o===i){P=null;break e}var v=o.sibling;if(v!==null){v.return=o.return,P=v;break e}P=o.return}}if(I=l,_t(),Je&&typeof Je.onPostCommitFiberRoot=="function")try{Je.onPostCommitFiberRoot(Is,e)}catch{}n=!0}return n}finally{D=r,Le.transition=t}}return!1}function Bo(e,t,r){t=wr(r,t),t=Hu(e,t,1),e=jt(e,t,1),t=fe(),e!==null&&(jn(e,1,t),Se(e,t))}function K(e,t,r){if(e.tag===3)Bo(e,e,r);else for(;t!==null;){if(t.tag===3){Bo(t,e,r);break}else if(t.tag===1){var n=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof n.componentDidCatch=="function"&&(Nt===null||!Nt.has(n))){e=wr(r,e),e=Qu(t,e,1),t=jt(t,e,1),e=fe(),t!==null&&(jn(t,1,e),Se(t,e));break}}t=t.return}}function mp(e,t,r){var n=e.pingCache;n!==null&&n.delete(t),t=fe(),e.pingedLanes|=e.suspendedLanes&r,se===e&&(ae&r)===r&&(re===4||re===3&&(ae&130023424)===ae&&500>q()-vi?It(e,0):yi|=r),Se(e,t)}function md(e,t){t===0&&(e.mode&1?(t=zn,zn<<=1,!(zn&130023424)&&(zn=4194304)):t=1);var r=fe();e=st(e,t),e!==null&&(jn(e,t,r),Se(e,r))}function fp(e){var t=e.memoizedState,r=0;t!==null&&(r=t.retryLane),md(e,r)}function pp(e,t){var r=0;switch(e.tag){case 13:var n=e.stateNode,l=e.memoizedState;l!==null&&(r=l.retryLane);break;case 19:n=e.stateNode;break;default:throw Error(S(314))}n!==null&&n.delete(t),md(e,r)}var fd;fd=function(e,t,r){if(e!==null)if(e.memoizedProps!==t.pendingProps||Ne.current)je=!0;else{if(!(e.lanes&r)&&!(t.flags&128))return je=!1,tp(e,t,r);je=!!(e.flags&131072)}else je=!1,W&&t.flags&1048576&&gu(t,gs,t.index);switch(t.lanes=0,t.tag){case 2:var n=t.type;ts(e,t),e=t.pendingProps;var l=yr(t,de.current);hr(t,r),l=mi(null,t,n,e,l,r);var a=fi();return t.flags|=1,typeof l=="object"&&l!==null&&typeof l.render=="function"&&l.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,we(n)?(a=!0,hs(t)):a=!1,t.memoizedState=l.state!==null&&l.state!==void 0?l.state:null,ii(t),l.updater=Ws,t.stateNode=l,l._reactInternals=t,ma(t,n,e,r),t=ha(null,t,n,!0,a,r)):(t.tag=0,W&&a&&ei(t),me(null,t,l,r),t=t.child),t;case 16:n=t.elementType;e:{switch(ts(e,t),e=t.pendingProps,l=n._init,n=l(n._payload),t.type=n,l=t.tag=xp(n),e=Ie(n,e),l){case 0:t=pa(null,t,n,e,r);break e;case 1:t=Oo(null,t,n,e,r);break e;case 11:t=To(null,t,n,e,r);break e;case 14:t=Ro(null,t,n,Ie(n.type,e),r);break e}throw Error(S(306,n,""))}return t;case 0:return n=t.type,l=t.pendingProps,l=t.elementType===n?l:Ie(n,l),pa(e,t,n,l,r);case 1:return n=t.type,l=t.pendingProps,l=t.elementType===n?l:Ie(n,l),Oo(e,t,n,l,r);case 3:e:{if(Ku(t),e===null)throw Error(S(387));n=t.pendingProps,a=t.memoizedState,l=a.element,Su(e,t),js(t,n,null,r);var i=t.memoizedState;if(n=i.element,a.isDehydrated)if(a={element:n,isDehydrated:!1,cache:i.cache,pendingSuspenseBoundaries:i.pendingSuspenseBoundaries,transitions:i.transitions},t.updateQueue.baseState=a,t.memoizedState=a,t.flags&256){l=wr(Error(S(423)),t),t=Lo(e,t,n,r,l);break e}else if(n!==l){l=wr(Error(S(424)),t),t=Lo(e,t,n,r,l);break e}else for(Ce=vt(t.stateNode.containerInfo.firstChild),be=t,W=!0,Fe=null,r=Nu(t,null,n,r),t.child=r;r;)r.flags=r.flags&-3|4096,r=r.sibling;else{if(vr(),n===l){t=lt(e,t,r);break e}me(e,t,n,r)}t=t.child}return t;case 5:return ku(t),e===null&&ca(t),n=t.type,l=t.pendingProps,a=e!==null?e.memoizedProps:null,i=l.children,sa(n,l)?i=null:a!==null&&sa(n,a)&&(t.flags|=32),Gu(e,t),me(e,t,i,r),t.child;case 6:return e===null&&ca(t),null;case 13:return qu(e,t,r);case 4:return oi(t,t.stateNode.containerInfo),n=t.pendingProps,e===null?t.child=jr(t,null,n,r):me(e,t,n,r),t.child;case 11:return n=t.type,l=t.pendingProps,l=t.elementType===n?l:Ie(n,l),To(e,t,n,l,r);case 7:return me(e,t,t.pendingProps,r),t.child;case 8:return me(e,t,t.pendingProps.children,r),t.child;case 12:return me(e,t,t.pendingProps.children,r),t.child;case 10:e:{if(n=t.type._context,l=t.pendingProps,a=t.memoizedProps,i=l.value,$(ys,n._currentValue),n._currentValue=i,a!==null)if(Be(a.value,i)){if(a.children===l.children&&!Ne.current){t=lt(e,t,r);break e}}else for(a=t.child,a!==null&&(a.return=t);a!==null;){var o=a.dependencies;if(o!==null){i=a.child;for(var c=o.firstContext;c!==null;){if(c.context===n){if(a.tag===1){c=tt(-1,r&-r),c.tag=2;var u=a.updateQueue;if(u!==null){u=u.shared;var f=u.pending;f===null?c.next=c:(c.next=f.next,f.next=c),u.pending=c}}a.lanes|=r,c=a.alternate,c!==null&&(c.lanes|=r),ua(a.return,r,t),o.lanes|=r;break}c=c.next}}else if(a.tag===10)i=a.type===t.type?null:a.child;else if(a.tag===18){if(i=a.return,i===null)throw Error(S(341));i.lanes|=r,o=i.alternate,o!==null&&(o.lanes|=r),ua(i,r,t),i=a.sibling}else i=a.child;if(i!==null)i.return=a;else for(i=a;i!==null;){if(i===t){i=null;break}if(a=i.sibling,a!==null){a.return=i.return,i=a;break}i=i.return}a=i}me(e,t,l.children,r),t=t.child}return t;case 9:return l=t.type,n=t.pendingProps.children,hr(t,r),l=Me(l),n=n(l),t.flags|=1,me(e,t,n,r),t.child;case 14:return n=t.type,l=Ie(n,t.pendingProps),l=Ie(n.type,l),Ro(e,t,n,l,r);case 15:return Yu(e,t,t.type,t.pendingProps,r);case 17:return n=t.type,l=t.pendingProps,l=t.elementType===n?l:Ie(n,l),ts(e,t),t.tag=1,we(n)?(e=!0,hs(t)):e=!1,hr(t,r),Wu(t,n,l),ma(t,n,l,r),ha(null,t,n,!0,e,r);case 19:return Xu(e,t,r);case 22:return Ju(e,t,r)}throw Error(S(156,t.tag))};function pd(e,t){return Uc(e,t)}function hp(e,t,r,n){this.tag=e,this.key=r,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=n,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Oe(e,t,r,n){return new hp(e,t,r,n)}function Si(e){return e=e.prototype,!(!e||!e.isReactComponent)}function xp(e){if(typeof e=="function")return Si(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Ba)return 11;if(e===Va)return 14}return 2}function St(e,t){var r=e.alternate;return r===null?(r=Oe(e.tag,t,e.key,e.mode),r.elementType=e.elementType,r.type=e.type,r.stateNode=e.stateNode,r.alternate=e,e.alternate=r):(r.pendingProps=t,r.type=e.type,r.flags=0,r.subtreeFlags=0,r.deletions=null),r.flags=e.flags&14680064,r.childLanes=e.childLanes,r.lanes=e.lanes,r.child=e.child,r.memoizedProps=e.memoizedProps,r.memoizedState=e.memoizedState,r.updateQueue=e.updateQueue,t=e.dependencies,r.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},r.sibling=e.sibling,r.index=e.index,r.ref=e.ref,r}function ss(e,t,r,n,l,a){var i=2;if(n=e,typeof e=="function")Si(e)&&(i=1);else if(typeof e=="string")i=5;else e:switch(e){case er:return Dt(r.children,l,a,t);case Ua:i=8,l|=8;break;case Al:return e=Oe(12,r,t,l|2),e.elementType=Al,e.lanes=a,e;case Il:return e=Oe(13,r,t,l),e.elementType=Il,e.lanes=a,e;case Dl:return e=Oe(19,r,t,l),e.elementType=Dl,e.lanes=a,e;case kc:return Ys(r,l,a,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case wc:i=10;break e;case Sc:i=9;break e;case Ba:i=11;break e;case Va:i=14;break e;case ct:i=16,n=null;break e}throw Error(S(130,e==null?e:typeof e,""))}return t=Oe(i,r,t,l),t.elementType=e,t.type=n,t.lanes=a,t}function Dt(e,t,r,n){return e=Oe(7,e,n,t),e.lanes=r,e}function Ys(e,t,r,n){return e=Oe(22,e,n,t),e.elementType=kc,e.lanes=r,e.stateNode={isHidden:!1},e}function Pl(e,t,r){return e=Oe(6,e,null,t),e.lanes=r,e}function _l(e,t,r){return t=Oe(4,e.children!==null?e.children:[],e.key,t),t.lanes=r,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function gp(e,t,r,n,l){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=cl(0),this.expirationTimes=cl(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=cl(0),this.identifierPrefix=n,this.onRecoverableError=l,this.mutableSourceEagerHydrationData=null}function ki(e,t,r,n,l,a,i,o,c){return e=new gp(e,t,r,o,c),t===1?(t=1,a===!0&&(t|=8)):t=0,a=Oe(3,null,null,t),e.current=a,a.stateNode=e,a.memoizedState={element:n,isDehydrated:r,cache:null,transitions:null,pendingSuspenseBoundaries:null},ii(a),e}function yp(e,t,r){var n=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Zt,key:n==null?null:""+n,children:e,containerInfo:t,implementation:r}}function hd(e){if(!e)return bt;e=e._reactInternals;e:{if(Qt(e)!==e||e.tag!==1)throw Error(S(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(we(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(S(171))}if(e.tag===1){var r=e.type;if(we(r))return hu(e,r,t)}return t}function xd(e,t,r,n,l,a,i,o,c){return e=ki(r,n,!0,e,l,a,i,o,c),e.context=hd(null),r=e.current,n=fe(),l=wt(r),a=tt(n,l),a.callback=t??null,jt(r,a,l),e.current.lanes=l,jn(e,l,n),Se(e,n),e}function Js(e,t,r,n){var l=t.current,a=fe(),i=wt(l);return r=hd(r),t.context===null?t.context=r:t.pendingContext=r,t=tt(a,i),t.payload={element:e},n=n===void 0?null:n,n!==null&&(t.callback=n),e=jt(l,t,i),e!==null&&(Ue(e,l,i,a),Xn(e,l,i)),i}function Ps(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Vo(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var r=e.retryLane;e.retryLane=r!==0&&r<t?r:t}}function Ci(e,t){Vo(e,t),(e=e.alternate)&&Vo(e,t)}function vp(){return null}var gd=typeof reportError=="function"?reportError:function(e){console.error(e)};function bi(e){this._internalRoot=e}Gs.prototype.render=bi.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(S(409));Js(e,t,null,null)};Gs.prototype.unmount=bi.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Vt(function(){Js(null,e,null,null)}),t[nt]=null}};function Gs(e){this._internalRoot=e}Gs.prototype.unstable_scheduleHydration=function(e){if(e){var t=Jc();e={blockedOn:null,target:e,priority:t};for(var r=0;r<dt.length&&t!==0&&t<dt[r].priority;r++);dt.splice(r,0,e),r===0&&Kc(e)}};function Ei(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Ks(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Wo(){}function jp(e,t,r,n,l){if(l){if(typeof n=="function"){var a=n;n=function(){var u=Ps(i);a.call(u)}}var i=xd(t,n,e,0,null,!1,!1,"",Wo);return e._reactRootContainer=i,e[nt]=i.current,nn(e.nodeType===8?e.parentNode:e),Vt(),i}for(;l=e.lastChild;)e.removeChild(l);if(typeof n=="function"){var o=n;n=function(){var u=Ps(c);o.call(u)}}var c=ki(e,0,!1,null,null,!1,!1,"",Wo);return e._reactRootContainer=c,e[nt]=c.current,nn(e.nodeType===8?e.parentNode:e),Vt(function(){Js(t,c,r,n)}),c}function qs(e,t,r,n,l){var a=r._reactRootContainer;if(a){var i=a;if(typeof l=="function"){var o=l;l=function(){var c=Ps(i);o.call(c)}}Js(t,i,e,l)}else i=jp(r,t,e,l,n);return Ps(i)}Qc=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var r=Dr(t.pendingLanes);r!==0&&(Qa(t,r|1),Se(t,q()),!(I&6)&&(Sr=q()+500,_t()))}break;case 13:Vt(function(){var n=st(e,1);if(n!==null){var l=fe();Ue(n,e,1,l)}}),Ci(e,1)}};Ya=function(e){if(e.tag===13){var t=st(e,134217728);if(t!==null){var r=fe();Ue(t,e,134217728,r)}Ci(e,134217728)}};Yc=function(e){if(e.tag===13){var t=wt(e),r=st(e,t);if(r!==null){var n=fe();Ue(r,e,t,n)}Ci(e,t)}};Jc=function(){return D};Gc=function(e,t){var r=D;try{return D=e,t()}finally{D=r}};Jl=function(e,t,r){switch(t){case"input":if(Ul(e,r),t=r.name,r.type==="radio"&&t!=null){for(r=e;r.parentNode;)r=r.parentNode;for(r=r.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<r.length;t++){var n=r[t];if(n!==e&&n.form===e.form){var l=Us(n);if(!l)throw Error(S(90));bc(n),Ul(n,l)}}}break;case"textarea":Pc(e,r);break;case"select":t=r.value,t!=null&&dr(e,!!r.multiple,t,!1)}};zc=ji;Ac=Vt;var Np={usingClientEntryPoint:!1,Events:[wn,sr,Us,Lc,Mc,ji]},zr={findFiberByHostInstance:Mt,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},wp={bundleType:zr.bundleType,version:zr.version,rendererPackageName:zr.rendererPackageName,rendererConfig:zr.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:at.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Fc(e),e===null?null:e.stateNode},findFiberByHostInstance:zr.findFiberByHostInstance||vp,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Qn=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Qn.isDisabled&&Qn.supportsFiber)try{Is=Qn.inject(wp),Je=Qn}catch{}}Pe.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Np;Pe.createPortal=function(e,t){var r=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Ei(t))throw Error(S(200));return yp(e,t,null,r)};Pe.createRoot=function(e,t){if(!Ei(e))throw Error(S(299));var r=!1,n="",l=gd;return t!=null&&(t.unstable_strictMode===!0&&(r=!0),t.identifierPrefix!==void 0&&(n=t.identifierPrefix),t.onRecoverableError!==void 0&&(l=t.onRecoverableError)),t=ki(e,1,!1,null,null,r,!1,n,l),e[nt]=t.current,nn(e.nodeType===8?e.parentNode:e),new bi(t)};Pe.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(S(188)):(e=Object.keys(e).join(","),Error(S(268,e)));return e=Fc(t),e=e===null?null:e.stateNode,e};Pe.flushSync=function(e){return Vt(e)};Pe.hydrate=function(e,t,r){if(!Ks(t))throw Error(S(200));return qs(null,e,t,!0,r)};Pe.hydrateRoot=function(e,t,r){if(!Ei(e))throw Error(S(405));var n=r!=null&&r.hydratedSources||null,l=!1,a="",i=gd;if(r!=null&&(r.unstable_strictMode===!0&&(l=!0),r.identifierPrefix!==void 0&&(a=r.identifierPrefix),r.onRecoverableError!==void 0&&(i=r.onRecoverableError)),t=xd(t,null,e,1,r??null,l,!1,a,i),e[nt]=t.current,nn(e),n)for(e=0;e<n.length;e++)r=n[e],l=r._getVersion,l=l(r._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[r,l]:t.mutableSourceEagerHydrationData.push(r,l);return new Gs(t)};Pe.render=function(e,t,r){if(!Ks(t))throw Error(S(200));return qs(null,e,t,!1,r)};Pe.unmountComponentAtNode=function(e){if(!Ks(e))throw Error(S(40));return e._reactRootContainer?(Vt(function(){qs(null,null,e,!1,function(){e._reactRootContainer=null,e[nt]=null})}),!0):!1};Pe.unstable_batchedUpdates=ji;Pe.unstable_renderSubtreeIntoContainer=function(e,t,r,n){if(!Ks(r))throw Error(S(200));if(e==null||e._reactInternals===void 0)throw Error(S(38));return qs(e,t,r,!1,n)};Pe.version="18.3.1-next-f1338f8080-20240426";function yd(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(yd)}catch(e){console.error(e)}}yd(),yc.exports=Pe;var Sp=yc.exports,Ho=Sp;Ml.createRoot=Ho.createRoot,Ml.hydrateRoot=Ho.hydrateRoot;/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function fn(){return fn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},fn.apply(this,arguments)}var ht;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(ht||(ht={}));const Qo="popstate";function kp(e){e===void 0&&(e={});function t(l,a){let{pathname:i="/",search:o="",hash:c=""}=Yt(l.location.hash.substr(1));return!i.startsWith("/")&&!i.startsWith(".")&&(i="/"+i),Ea("",{pathname:i,search:o,hash:c},a.state&&a.state.usr||null,a.state&&a.state.key||"default")}function r(l,a){let i=l.document.querySelector("base"),o="";if(i&&i.getAttribute("href")){let c=l.location.href,u=c.indexOf("#");o=u===-1?c:c.slice(0,u)}return o+"#"+(typeof a=="string"?a:_s(a))}function n(l,a){Pi(l.pathname.charAt(0)==="/","relative pathnames are not supported in hash history.push("+JSON.stringify(a)+")")}return bp(t,r,n,e)}function ee(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function Pi(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function Cp(){return Math.random().toString(36).substr(2,8)}function Yo(e,t){return{usr:e.state,key:e.key,idx:t}}function Ea(e,t,r,n){return r===void 0&&(r=null),fn({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?Yt(t):t,{state:r,key:t&&t.key||n||Cp()})}function _s(e){let{pathname:t="/",search:r="",hash:n=""}=e;return r&&r!=="?"&&(t+=r.charAt(0)==="?"?r:"?"+r),n&&n!=="#"&&(t+=n.charAt(0)==="#"?n:"#"+n),t}function Yt(e){let t={};if(e){let r=e.indexOf("#");r>=0&&(t.hash=e.substr(r),e=e.substr(0,r));let n=e.indexOf("?");n>=0&&(t.search=e.substr(n),e=e.substr(0,n)),e&&(t.pathname=e)}return t}function bp(e,t,r,n){n===void 0&&(n={});let{window:l=document.defaultView,v5Compat:a=!1}=n,i=l.history,o=ht.Pop,c=null,u=f();u==null&&(u=0,i.replaceState(fn({},i.state,{idx:u}),""));function f(){return(i.state||{idx:null}).idx}function m(){o=ht.Pop;let w=f(),p=w==null?null:w-u;u=w,c&&c({action:o,location:y.location,delta:p})}function h(w,p){o=ht.Push;let d=Ea(y.location,w,p);r&&r(d,w),u=f()+1;let x=Yo(d,u),v=y.createHref(d);try{i.pushState(x,"",v)}catch(k){if(k instanceof DOMException&&k.name==="DataCloneError")throw k;l.location.assign(v)}a&&c&&c({action:o,location:y.location,delta:1})}function g(w,p){o=ht.Replace;let d=Ea(y.location,w,p);r&&r(d,w),u=f();let x=Yo(d,u),v=y.createHref(d);i.replaceState(x,"",v),a&&c&&c({action:o,location:y.location,delta:0})}function j(w){let p=l.location.origin!=="null"?l.location.origin:l.location.href,d=typeof w=="string"?w:_s(w);return d=d.replace(/ $/,"%20"),ee(p,"No window.location.(origin|href) available to create URL for href: "+d),new URL(d,p)}let y={get action(){return o},get location(){return e(l,i)},listen(w){if(c)throw new Error("A history only accepts one active listener");return l.addEventListener(Qo,m),c=w,()=>{l.removeEventListener(Qo,m),c=null}},createHref(w){return t(l,w)},createURL:j,encodeLocation(w){let p=j(w);return{pathname:p.pathname,search:p.search,hash:p.hash}},push:h,replace:g,go(w){return i.go(w)}};return y}var Jo;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(Jo||(Jo={}));function Ep(e,t,r){return r===void 0&&(r="/"),Pp(e,t,r,!1)}function Pp(e,t,r,n){let l=typeof t=="string"?Yt(t):t,a=_i(l.pathname||"/",r);if(a==null)return null;let i=vd(e);_p(i);let o=null;for(let c=0;o==null&&c<i.length;++c){let u=$p(a);o=Dp(i[c],u,n)}return o}function vd(e,t,r,n){t===void 0&&(t=[]),r===void 0&&(r=[]),n===void 0&&(n="");let l=(a,i,o)=>{let c={relativePath:o===void 0?a.path||"":o,caseSensitive:a.caseSensitive===!0,childrenIndex:i,route:a};c.relativePath.startsWith("/")&&(ee(c.relativePath.startsWith(n),'Absolute route path "'+c.relativePath+'" nested under path '+('"'+n+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),c.relativePath=c.relativePath.slice(n.length));let u=kt([n,c.relativePath]),f=r.concat(c);a.children&&a.children.length>0&&(ee(a.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+u+'".')),vd(a.children,t,f,u)),!(a.path==null&&!a.index)&&t.push({path:u,score:Ap(u,a.index),routesMeta:f})};return e.forEach((a,i)=>{var o;if(a.path===""||!((o=a.path)!=null&&o.includes("?")))l(a,i);else for(let c of jd(a.path))l(a,i,c)}),t}function jd(e){let t=e.split("/");if(t.length===0)return[];let[r,...n]=t,l=r.endsWith("?"),a=r.replace(/\?$/,"");if(n.length===0)return l?[a,""]:[a];let i=jd(n.join("/")),o=[];return o.push(...i.map(c=>c===""?a:[a,c].join("/"))),l&&o.push(...i),o.map(c=>e.startsWith("/")&&c===""?"/":c)}function _p(e){e.sort((t,r)=>t.score!==r.score?r.score-t.score:Ip(t.routesMeta.map(n=>n.childrenIndex),r.routesMeta.map(n=>n.childrenIndex)))}const Tp=/^:[\w-]+$/,Rp=3,Op=2,Lp=1,Mp=10,zp=-2,Go=e=>e==="*";function Ap(e,t){let r=e.split("/"),n=r.length;return r.some(Go)&&(n+=zp),t&&(n+=Op),r.filter(l=>!Go(l)).reduce((l,a)=>l+(Tp.test(a)?Rp:a===""?Lp:Mp),n)}function Ip(e,t){return e.length===t.length&&e.slice(0,-1).every((n,l)=>n===t[l])?e[e.length-1]-t[t.length-1]:0}function Dp(e,t,r){r===void 0&&(r=!1);let{routesMeta:n}=e,l={},a="/",i=[];for(let o=0;o<n.length;++o){let c=n[o],u=o===n.length-1,f=a==="/"?t:t.slice(a.length)||"/",m=Ko({path:c.relativePath,caseSensitive:c.caseSensitive,end:u},f),h=c.route;if(!m&&u&&r&&!n[n.length-1].route.index&&(m=Ko({path:c.relativePath,caseSensitive:c.caseSensitive,end:!1},f)),!m)return null;Object.assign(l,m.params),i.push({params:l,pathname:kt([a,m.pathname]),pathnameBase:Wp(kt([a,m.pathnameBase])),route:h}),m.pathnameBase!=="/"&&(a=kt([a,m.pathnameBase]))}return i}function Ko(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[r,n]=Fp(e.path,e.caseSensitive,e.end),l=t.match(r);if(!l)return null;let a=l[0],i=a.replace(/(.)\/+$/,"$1"),o=l.slice(1);return{params:n.reduce((u,f,m)=>{let{paramName:h,isOptional:g}=f;if(h==="*"){let y=o[m]||"";i=a.slice(0,a.length-y.length).replace(/(.)\/+$/,"$1")}const j=o[m];return g&&!j?u[h]=void 0:u[h]=(j||"").replace(/%2F/g,"/"),u},{}),pathname:a,pathnameBase:i,pattern:e}}function Fp(e,t,r){t===void 0&&(t=!1),r===void 0&&(r=!0),Pi(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let n=[],l="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(i,o,c)=>(n.push({paramName:o,isOptional:c!=null}),c?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(n.push({paramName:"*"}),l+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):r?l+="\\/*$":e!==""&&e!=="/"&&(l+="(?:(?=\\/|$))"),[new RegExp(l,t?void 0:"i"),n]}function $p(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return Pi(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function _i(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let r=t.endsWith("/")?t.length-1:t.length,n=e.charAt(r);return n&&n!=="/"?null:e.slice(r)||"/"}function Up(e,t){t===void 0&&(t="/");let{pathname:r,search:n="",hash:l=""}=typeof e=="string"?Yt(e):e;return{pathname:r?r.startsWith("/")?r:Bp(r,t):t,search:Hp(n),hash:Qp(l)}}function Bp(e,t){let r=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(l=>{l===".."?r.length>1&&r.pop():l!=="."&&r.push(l)}),r.length>1?r.join("/"):"/"}function Tl(e,t,r,n){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(n)+"].  Please separate it out to the ")+("`to."+r+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function Vp(e){return e.filter((t,r)=>r===0||t.route.path&&t.route.path.length>0)}function Nd(e,t){let r=Vp(e);return t?r.map((n,l)=>l===r.length-1?n.pathname:n.pathnameBase):r.map(n=>n.pathnameBase)}function wd(e,t,r,n){n===void 0&&(n=!1);let l;typeof e=="string"?l=Yt(e):(l=fn({},e),ee(!l.pathname||!l.pathname.includes("?"),Tl("?","pathname","search",l)),ee(!l.pathname||!l.pathname.includes("#"),Tl("#","pathname","hash",l)),ee(!l.search||!l.search.includes("#"),Tl("#","search","hash",l)));let a=e===""||l.pathname==="",i=a?"/":l.pathname,o;if(i==null)o=r;else{let m=t.length-1;if(!n&&i.startsWith("..")){let h=i.split("/");for(;h[0]==="..";)h.shift(),m-=1;l.pathname=h.join("/")}o=m>=0?t[m]:"/"}let c=Up(l,o),u=i&&i!=="/"&&i.endsWith("/"),f=(a||i===".")&&r.endsWith("/");return!c.pathname.endsWith("/")&&(u||f)&&(c.pathname+="/"),c}const kt=e=>e.join("/").replace(/\/\/+/g,"/"),Wp=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),Hp=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,Qp=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function Yp(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const Sd=["post","put","patch","delete"];new Set(Sd);const Jp=["get",...Sd];new Set(Jp);/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function pn(){return pn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},pn.apply(this,arguments)}const Ti=N.createContext(null),Gp=N.createContext(null),Jt=N.createContext(null),Xs=N.createContext(null),Tt=N.createContext({outlet:null,matches:[],isDataRoute:!1}),kd=N.createContext(null);function Kp(e,t){let{relative:r}=t===void 0?{}:t;kn()||ee(!1);let{basename:n,navigator:l}=N.useContext(Jt),{hash:a,pathname:i,search:o}=bd(e,{relative:r}),c=i;return n!=="/"&&(c=i==="/"?n:kt([n,i])),l.createHref({pathname:c,search:o,hash:a})}function kn(){return N.useContext(Xs)!=null}function Cn(){return kn()||ee(!1),N.useContext(Xs).location}function Cd(e){N.useContext(Jt).static||N.useLayoutEffect(e)}function bn(){let{isDataRoute:e}=N.useContext(Tt);return e?uh():qp()}function qp(){kn()||ee(!1);let e=N.useContext(Ti),{basename:t,future:r,navigator:n}=N.useContext(Jt),{matches:l}=N.useContext(Tt),{pathname:a}=Cn(),i=JSON.stringify(Nd(l,r.v7_relativeSplatPath)),o=N.useRef(!1);return Cd(()=>{o.current=!0}),N.useCallback(function(u,f){if(f===void 0&&(f={}),!o.current)return;if(typeof u=="number"){n.go(u);return}let m=wd(u,JSON.parse(i),a,f.relative==="path");e==null&&t!=="/"&&(m.pathname=m.pathname==="/"?t:kt([t,m.pathname])),(f.replace?n.replace:n.push)(m,f.state,f)},[t,n,i,a,e])}function Xp(){let{matches:e}=N.useContext(Tt),t=e[e.length-1];return t?t.params:{}}function bd(e,t){let{relative:r}=t===void 0?{}:t,{future:n}=N.useContext(Jt),{matches:l}=N.useContext(Tt),{pathname:a}=Cn(),i=JSON.stringify(Nd(l,n.v7_relativeSplatPath));return N.useMemo(()=>wd(e,JSON.parse(i),a,r==="path"),[e,i,a,r])}function Zp(e,t){return eh(e,t)}function eh(e,t,r,n){kn()||ee(!1);let{navigator:l}=N.useContext(Jt),{matches:a}=N.useContext(Tt),i=a[a.length-1],o=i?i.params:{};i&&i.pathname;let c=i?i.pathnameBase:"/";i&&i.route;let u=Cn(),f;if(t){var m;let w=typeof t=="string"?Yt(t):t;c==="/"||(m=w.pathname)!=null&&m.startsWith(c)||ee(!1),f=w}else f=u;let h=f.pathname||"/",g=h;if(c!=="/"){let w=c.replace(/^\//,"").split("/");g="/"+h.replace(/^\//,"").split("/").slice(w.length).join("/")}let j=Ep(e,{pathname:g}),y=lh(j&&j.map(w=>Object.assign({},w,{params:Object.assign({},o,w.params),pathname:kt([c,l.encodeLocation?l.encodeLocation(w.pathname).pathname:w.pathname]),pathnameBase:w.pathnameBase==="/"?c:kt([c,l.encodeLocation?l.encodeLocation(w.pathnameBase).pathname:w.pathnameBase])})),a,r,n);return t&&y?N.createElement(Xs.Provider,{value:{location:pn({pathname:"/",search:"",hash:"",state:null,key:"default"},f),navigationType:ht.Pop}},y):y}function th(){let e=ch(),t=Yp(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),r=e instanceof Error?e.stack:null,l={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"},a=null;return N.createElement(N.Fragment,null,N.createElement("h2",null,"Unexpected Application Error!"),N.createElement("h3",{style:{fontStyle:"italic"}},t),r?N.createElement("pre",{style:l},r):null,a)}const rh=N.createElement(th,null);class nh extends N.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,r){return r.location!==t.location||r.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:r.error,location:r.location,revalidation:t.revalidation||r.revalidation}}componentDidCatch(t,r){console.error("React Router caught the following error during render",t,r)}render(){return this.state.error!==void 0?N.createElement(Tt.Provider,{value:this.props.routeContext},N.createElement(kd.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function sh(e){let{routeContext:t,match:r,children:n}=e,l=N.useContext(Ti);return l&&l.static&&l.staticContext&&(r.route.errorElement||r.route.ErrorBoundary)&&(l.staticContext._deepestRenderedBoundaryId=r.route.id),N.createElement(Tt.Provider,{value:t},n)}function lh(e,t,r,n){var l;if(t===void 0&&(t=[]),r===void 0&&(r=null),n===void 0&&(n=null),e==null){var a;if(!r)return null;if(r.errors)e=r.matches;else if((a=n)!=null&&a.v7_partialHydration&&t.length===0&&!r.initialized&&r.matches.length>0)e=r.matches;else return null}let i=e,o=(l=r)==null?void 0:l.errors;if(o!=null){let f=i.findIndex(m=>m.route.id&&(o==null?void 0:o[m.route.id])!==void 0);f>=0||ee(!1),i=i.slice(0,Math.min(i.length,f+1))}let c=!1,u=-1;if(r&&n&&n.v7_partialHydration)for(let f=0;f<i.length;f++){let m=i[f];if((m.route.HydrateFallback||m.route.hydrateFallbackElement)&&(u=f),m.route.id){let{loaderData:h,errors:g}=r,j=m.route.loader&&h[m.route.id]===void 0&&(!g||g[m.route.id]===void 0);if(m.route.lazy||j){c=!0,u>=0?i=i.slice(0,u+1):i=[i[0]];break}}}return i.reduceRight((f,m,h)=>{let g,j=!1,y=null,w=null;r&&(g=o&&m.route.id?o[m.route.id]:void 0,y=m.route.errorElement||rh,c&&(u<0&&h===0?(dh("route-fallback",!1),j=!0,w=null):u===h&&(j=!0,w=m.route.hydrateFallbackElement||null)));let p=t.concat(i.slice(0,h+1)),d=()=>{let x;return g?x=y:j?x=w:m.route.Component?x=N.createElement(m.route.Component,null):m.route.element?x=m.route.element:x=f,N.createElement(sh,{match:m,routeContext:{outlet:f,matches:p,isDataRoute:r!=null},children:x})};return r&&(m.route.ErrorBoundary||m.route.errorElement||h===0)?N.createElement(nh,{location:r.location,revalidation:r.revalidation,component:y,error:g,children:d(),routeContext:{outlet:null,matches:p,isDataRoute:!0}}):d()},null)}var Ed=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(Ed||{}),Ts=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(Ts||{});function ah(e){let t=N.useContext(Ti);return t||ee(!1),t}function ih(e){let t=N.useContext(Gp);return t||ee(!1),t}function oh(e){let t=N.useContext(Tt);return t||ee(!1),t}function Pd(e){let t=oh(),r=t.matches[t.matches.length-1];return r.route.id||ee(!1),r.route.id}function ch(){var e;let t=N.useContext(kd),r=ih(Ts.UseRouteError),n=Pd(Ts.UseRouteError);return t!==void 0?t:(e=r.errors)==null?void 0:e[n]}function uh(){let{router:e}=ah(Ed.UseNavigateStable),t=Pd(Ts.UseNavigateStable),r=N.useRef(!1);return Cd(()=>{r.current=!0}),N.useCallback(function(l,a){a===void 0&&(a={}),r.current&&(typeof l=="number"?e.navigate(l):e.navigate(l,pn({fromRouteId:t},a)))},[e,t])}const qo={};function dh(e,t,r){!t&&!qo[e]&&(qo[e]=!0)}function mh(e,t){e==null||e.v7_startTransition,(e==null?void 0:e.v7_relativeSplatPath)===void 0&&(!t||t.v7_relativeSplatPath),t&&(t.v7_fetcherPersist,t.v7_normalizeFormMethod,t.v7_partialHydration,t.v7_skipActionErrorRevalidation)}function Xt(e){ee(!1)}function fh(e){let{basename:t="/",children:r=null,location:n,navigationType:l=ht.Pop,navigator:a,static:i=!1,future:o}=e;kn()&&ee(!1);let c=t.replace(/^\/*/,"/"),u=N.useMemo(()=>({basename:c,navigator:a,static:i,future:pn({v7_relativeSplatPath:!1},o)}),[c,o,a,i]);typeof n=="string"&&(n=Yt(n));let{pathname:f="/",search:m="",hash:h="",state:g=null,key:j="default"}=n,y=N.useMemo(()=>{let w=_i(f,c);return w==null?null:{location:{pathname:w,search:m,hash:h,state:g,key:j},navigationType:l}},[c,f,m,h,g,j,l]);return y==null?null:N.createElement(Jt.Provider,{value:u},N.createElement(Xs.Provider,{children:r,value:y}))}function ph(e){let{children:t,location:r}=e;return Zp(Pa(t),r)}new Promise(()=>{});function Pa(e,t){t===void 0&&(t=[]);let r=[];return N.Children.forEach(e,(n,l)=>{if(!N.isValidElement(n))return;let a=[...t,l];if(n.type===N.Fragment){r.push.apply(r,Pa(n.props.children,a));return}n.type!==Xt&&ee(!1),!n.props.index||!n.props.children||ee(!1);let i={id:n.props.id||a.join("-"),caseSensitive:n.props.caseSensitive,element:n.props.element,Component:n.props.Component,index:n.props.index,path:n.props.path,loader:n.props.loader,action:n.props.action,errorElement:n.props.errorElement,ErrorBoundary:n.props.ErrorBoundary,hasErrorBoundary:n.props.ErrorBoundary!=null||n.props.errorElement!=null,shouldRevalidate:n.props.shouldRevalidate,handle:n.props.handle,lazy:n.props.lazy};n.props.children&&(i.children=Pa(n.props.children,a)),r.push(i)}),r}/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function _a(){return _a=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},_a.apply(this,arguments)}function hh(e,t){if(e==null)return{};var r={},n=Object.keys(e),l,a;for(a=0;a<n.length;a++)l=n[a],!(t.indexOf(l)>=0)&&(r[l]=e[l]);return r}function xh(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function gh(e,t){return e.button===0&&(!t||t==="_self")&&!xh(e)}function Ta(e){return e===void 0&&(e=""),new URLSearchParams(typeof e=="string"||Array.isArray(e)||e instanceof URLSearchParams?e:Object.keys(e).reduce((t,r)=>{let n=e[r];return t.concat(Array.isArray(n)?n.map(l=>[r,l]):[[r,n]])},[]))}function yh(e,t){let r=Ta(e);return t&&t.forEach((n,l)=>{r.has(l)||t.getAll(l).forEach(a=>{r.append(l,a)})}),r}const vh=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],jh="6";try{window.__reactRouterVersion=jh}catch{}const Nh="startTransition",Xo=dm[Nh];function wh(e){let{basename:t,children:r,future:n,window:l}=e,a=N.useRef();a.current==null&&(a.current=kp({window:l,v5Compat:!0}));let i=a.current,[o,c]=N.useState({action:i.action,location:i.location}),{v7_startTransition:u}=n||{},f=N.useCallback(m=>{u&&Xo?Xo(()=>c(m)):c(m)},[c,u]);return N.useLayoutEffect(()=>i.listen(f),[i,f]),N.useEffect(()=>mh(n),[n]),N.createElement(fh,{basename:t,children:r,location:o.location,navigationType:o.action,navigator:i,future:n})}const Sh=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",kh=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,X=N.forwardRef(function(t,r){let{onClick:n,relative:l,reloadDocument:a,replace:i,state:o,target:c,to:u,preventScrollReset:f,viewTransition:m}=t,h=hh(t,vh),{basename:g}=N.useContext(Jt),j,y=!1;if(typeof u=="string"&&kh.test(u)&&(j=u,Sh))try{let x=new URL(window.location.href),v=u.startsWith("//")?new URL(x.protocol+u):new URL(u),k=_i(v.pathname,g);v.origin===x.origin&&k!=null?u=k+v.search+v.hash:y=!0}catch{}let w=Kp(u,{relative:l}),p=Ch(u,{replace:i,state:o,target:c,preventScrollReset:f,relative:l,viewTransition:m});function d(x){n&&n(x),x.defaultPrevented||p(x)}return N.createElement("a",_a({},h,{href:j||w,onClick:y||a?n:d,ref:r,target:c}))});var Zo;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(Zo||(Zo={}));var ec;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(ec||(ec={}));function Ch(e,t){let{target:r,replace:n,state:l,preventScrollReset:a,relative:i,viewTransition:o}=t===void 0?{}:t,c=bn(),u=Cn(),f=bd(e,{relative:i});return N.useCallback(m=>{if(gh(m,r)){m.preventDefault();let h=n!==void 0?n:_s(u)===_s(f);c(e,{replace:h,state:l,preventScrollReset:a,relative:i,viewTransition:o})}},[u,c,f,n,l,r,e,a,i,o])}function bh(e){let t=N.useRef(Ta(e)),r=N.useRef(!1),n=Cn(),l=N.useMemo(()=>yh(n.search,r.current?null:t.current),[n.search]),a=bn(),i=N.useCallback((o,c)=>{const u=Ta(typeof o=="function"?o(l):o);r.current=!0,a("?"+u,c)},[a,l]);return[l,i]}const _d=N.createContext(),Eh=(e,t)=>{switch(t.type){case"ADD_TO_CART":return e.items.find(n=>n.id===t.payload.id)?{...e,items:e.items.map(n=>n.id===t.payload.id?{...n,quantity:n.quantity+1}:n)}:{...e,items:[...e.items,{...t.payload,quantity:1}]};case"REMOVE_FROM_CART":return{...e,items:e.items.filter(n=>n.id!==t.payload)};case"UPDATE_QUANTITY":return{...e,items:e.items.map(n=>n.id===t.payload.id?{...n,quantity:t.payload.quantity}:n)};case"CLEAR_CART":return{...e,items:[]};default:return e}},Ph={items:[],isOpen:!1},_h=({children:e})=>{const[t,r]=N.useReducer(Eh,Ph),n=f=>{r({type:"ADD_TO_CART",payload:f})},l=f=>{r({type:"REMOVE_FROM_CART",payload:f})},a=(f,m)=>{m<=0?l(f):r({type:"UPDATE_QUANTITY",payload:{id:f,quantity:m}})},i=()=>{r({type:"CLEAR_CART"})},o=()=>t.items.reduce((f,m)=>f+m.price*m.quantity,0),c=()=>t.items.reduce((f,m)=>f+m.quantity,0),u={items:t.items,addToCart:n,removeFromCart:l,updateQuantity:a,clearCart:i,getCartTotal:o,getCartItemsCount:c};return s.jsx(_d.Provider,{value:u,children:e})},En=()=>{const e=N.useContext(_d);if(!e)throw new Error("useCart must be used within a CartProvider");return e},tc=[{id:1,name:"Premium Cotton T-Shirt",price:29.99,originalPrice:39.99,category:"Men",image:"https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=400&h=400&fit=crop",description:"Comfortable premium cotton t-shirt with modern fit. Perfect for casual wear.",sizes:["S","M","L","XL"],colors:["Black","White","Gray","Navy"],rating:4.5,reviews:128,featured:!0},{id:2,name:"Elegant Summer Dress",price:79.99,originalPrice:99.99,category:"Women",image:"https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=400&h=400&fit=crop",description:"Beautiful flowing summer dress perfect for any occasion.",sizes:["XS","S","M","L"],colors:["Pink","Blue","White","Yellow"],rating:4.8,reviews:89,featured:!0},{id:3,name:"Leather Crossbody Bag",price:149.99,originalPrice:199.99,category:"Bags",image:"https://images.unsplash.com/photo-**********-98eeb64c6a62?w=400&h=400&fit=crop",description:"Genuine leather crossbody bag with adjustable strap and multiple compartments.",sizes:["One Size"],colors:["Brown","Black","Tan"],rating:4.7,reviews:156,featured:!0},{id:4,name:"Classic Denim Jacket",price:89.99,originalPrice:119.99,category:"Men",image:"https://images.unsplash.com/photo-1551028719-00167b16eac5?w=400&h=400&fit=crop",description:"Timeless denim jacket with vintage wash and comfortable fit.",sizes:["S","M","L","XL","XXL"],colors:["Blue","Black","Light Blue"],rating:4.6,reviews:203,featured:!1},{id:5,name:"Floral Print Blouse",price:49.99,originalPrice:69.99,category:"Women",image:"https://images.unsplash.com/photo-1564257577-0a8b7b8b6e8b?w=400&h=400&fit=crop",description:"Elegant floral print blouse with flowing sleeves.",sizes:["XS","S","M","L","XL"],colors:["Pink","Blue","White"],rating:4.4,reviews:67,featured:!1},{id:6,name:"Designer Tote Bag",price:199.99,originalPrice:249.99,category:"Bags",image:"https://images.unsplash.com/photo-1584917865442-de89df76afd3?w=400&h=400&fit=crop",description:"Spacious designer tote bag perfect for work or travel.",sizes:["One Size"],colors:["Black","Brown","Navy"],rating:4.9,reviews:234,featured:!1},{id:7,name:"Casual Sneakers",price:119.99,originalPrice:149.99,category:"Men",image:"https://images.unsplash.com/photo-1549298916-b41d501d3772?w=400&h=400&fit=crop",description:"Comfortable casual sneakers with modern design.",sizes:["7","8","9","10","11","12"],colors:["White","Black","Gray"],rating:4.3,reviews:145,featured:!1},{id:8,name:"Silk Scarf",price:39.99,originalPrice:59.99,category:"Women",image:"https://images.unsplash.com/photo-1601924994987-69e26d50dc26?w=400&h=400&fit=crop",description:"Luxurious silk scarf with beautiful pattern.",sizes:["One Size"],colors:["Pink","Blue","Gold","Red"],rating:4.6,reviews:78,featured:!1}],Td=N.createContext(),Th=(e,t)=>{switch(t.type){case"SET_PRODUCTS":return{...e,products:t.payload};case"ADD_PRODUCT":const r={...t.payload,id:Math.max(...e.products.map(i=>i.id))+1},n=[...e.products,r];return localStorage.setItem("ecommerce_products",JSON.stringify(n)),{...e,products:n};case"UPDATE_PRODUCT":const l=e.products.map(i=>i.id===t.payload.id?t.payload:i);return localStorage.setItem("ecommerce_products",JSON.stringify(l)),{...e,products:l};case"DELETE_PRODUCT":const a=e.products.filter(i=>i.id!==t.payload);return localStorage.setItem("ecommerce_products",JSON.stringify(a)),{...e,products:a};default:return e}},Rh={products:[],loading:!1,error:null},Oh=({children:e})=>{const[t,r]=N.useReducer(Th,Rh);N.useEffect(()=>{const u=localStorage.getItem("ecommerce_products");u?r({type:"SET_PRODUCTS",payload:JSON.parse(u)}):(r({type:"SET_PRODUCTS",payload:tc}),localStorage.setItem("ecommerce_products",JSON.stringify(tc)))},[]);const n=u=>{r({type:"ADD_PRODUCT",payload:u})},l=u=>{r({type:"UPDATE_PRODUCT",payload:u})},a=u=>{r({type:"DELETE_PRODUCT",payload:u})},i=u=>t.products.find(f=>f.id===parseInt(u)),o=u=>u==="all"?t.products:t.products.filter(f=>f.category.toLowerCase()===u.toLowerCase()),c={products:t.products,loading:t.loading,error:t.error,addProduct:n,updateProduct:l,deleteProduct:a,getProductById:i,getProductsByCategory:o};return s.jsx(Td.Provider,{value:c,children:e})},Zs=()=>{const e=N.useContext(Td);if(!e)throw new Error("useProducts must be used within a ProductProvider");return e},Rd=N.createContext(),rc=[{id:1,firstName:"John",lastName:"Doe",email:"<EMAIL>",phone:"+****************",address:"123 Main St, New York, NY 10001",dateJoined:"2024-01-15",totalOrders:12,totalSpent:1250.99,status:"Active",lastOrder:"2024-06-10",avatar:"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face"},{id:2,firstName:"Jane",lastName:"Smith",email:"<EMAIL>",phone:"+****************",address:"456 Oak Ave, Los Angeles, CA 90210",dateJoined:"2024-02-20",totalOrders:8,totalSpent:890.5,status:"Active",lastOrder:"2024-06-12",avatar:"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face"},{id:3,firstName:"Mike",lastName:"Johnson",email:"<EMAIL>",phone:"+****************",address:"789 Pine St, Chicago, IL 60601",dateJoined:"2024-03-10",totalOrders:5,totalSpent:425.75,status:"Active",lastOrder:"2024-06-08",avatar:"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face"},{id:4,firstName:"Sarah",lastName:"Wilson",email:"<EMAIL>",phone:"+****************",address:"321 Elm St, Miami, FL 33101",dateJoined:"2024-01-05",totalOrders:15,totalSpent:1850.25,status:"VIP",lastOrder:"2024-06-13",avatar:"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face"},{id:5,firstName:"David",lastName:"Brown",email:"<EMAIL>",phone:"+****************",address:"654 Maple Dr, Seattle, WA 98101",dateJoined:"2024-04-15",totalOrders:3,totalSpent:180,status:"Inactive",lastOrder:"2024-05-20",avatar:"https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=100&h=100&fit=crop&crop=face"}],Lh=(e,t)=>{switch(t.type){case"SET_CUSTOMERS":return{...e,customers:t.payload};case"ADD_CUSTOMER":const r={...t.payload,id:Math.max(...e.customers.map(o=>o.id))+1,dateJoined:new Date().toISOString().split("T")[0],totalOrders:0,totalSpent:0,status:"Active",lastOrder:null},n=[...e.customers,r];return localStorage.setItem("ecommerce_customers",JSON.stringify(n)),{...e,customers:n};case"UPDATE_CUSTOMER":const l=e.customers.map(o=>o.id===t.payload.id?t.payload:o);return localStorage.setItem("ecommerce_customers",JSON.stringify(l)),{...e,customers:l};case"DELETE_CUSTOMER":const a=e.customers.filter(o=>o.id!==t.payload);return localStorage.setItem("ecommerce_customers",JSON.stringify(a)),{...e,customers:a};case"UPDATE_CUSTOMER_STATS":const i=e.customers.map(o=>o.id===t.payload.customerId?{...o,totalOrders:o.totalOrders+1,totalSpent:o.totalSpent+t.payload.orderAmount,lastOrder:new Date().toISOString().split("T")[0],status:o.totalSpent+t.payload.orderAmount>1e3?"VIP":"Active"}:o);return localStorage.setItem("ecommerce_customers",JSON.stringify(i)),{...e,customers:i};default:return e}},Mh={customers:[],loading:!1,error:null},zh=({children:e})=>{const[t,r]=N.useReducer(Lh,Mh);N.useEffect(()=>{const m=localStorage.getItem("ecommerce_customers");m?r({type:"SET_CUSTOMERS",payload:JSON.parse(m)}):(r({type:"SET_CUSTOMERS",payload:rc}),localStorage.setItem("ecommerce_customers",JSON.stringify(rc)))},[]);const n=m=>{r({type:"ADD_CUSTOMER",payload:m})},l=m=>{r({type:"UPDATE_CUSTOMER",payload:m})},a=m=>{r({type:"DELETE_CUSTOMER",payload:m})},i=(m,h)=>{r({type:"UPDATE_CUSTOMER_STATS",payload:{customerId:m,orderAmount:h}})},o=m=>t.customers.find(h=>h.id===parseInt(m)),c=m=>m==="all"?t.customers:t.customers.filter(h=>h.status.toLowerCase()===m.toLowerCase()),u=()=>{const m=t.customers.length,h=t.customers.filter(p=>p.status==="Active").length,g=t.customers.filter(p=>p.status==="VIP").length,j=t.customers.filter(p=>p.status==="Inactive").length,y=t.customers.reduce((p,d)=>p+d.totalSpent,0),w=y/t.customers.reduce((p,d)=>p+d.totalOrders,0)||0;return{total:m,active:h,vip:g,inactive:j,totalRevenue:y,avgOrderValue:w}},f={customers:t.customers,loading:t.loading,error:t.error,addCustomer:n,updateCustomer:l,deleteCustomer:a,updateCustomerStats:i,getCustomerById:o,getCustomersByStatus:c,getCustomerStats:u};return s.jsx(Rd.Provider,{value:f,children:e})},Od=()=>{const e=N.useContext(Rd);if(!e)throw new Error("useCustomers must be used within a CustomerProvider");return e},Ld=N.createContext(),nc=[{id:1,name:"Men",slug:"men",description:"Men's fashion and accessories",active:!0},{id:2,name:"Women",slug:"women",description:"Women's fashion and accessories",active:!0},{id:3,name:"Bags",slug:"bags",description:"Handbags, backpacks, and accessories",active:!0},{id:4,name:"Shoes",slug:"shoes",description:"Footwear for all occasions",active:!0},{id:5,name:"Accessories",slug:"accessories",description:"Jewelry, watches, and more",active:!0}],Ah=(e,t)=>{switch(t.type){case"SET_CATEGORIES":return{...e,categories:t.payload};case"ADD_CATEGORY":const r={...t.payload,id:Math.max(...e.categories.map(o=>o.id),0)+1,slug:t.payload.name.toLowerCase().replace(/\s+/g,"-"),active:!0},n=[...e.categories,r];return localStorage.setItem("ecommerce_categories",JSON.stringify(n)),{...e,categories:n};case"UPDATE_CATEGORY":const l=e.categories.map(o=>o.id===t.payload.id?{...t.payload,slug:t.payload.name.toLowerCase().replace(/\s+/g,"-")}:o);return localStorage.setItem("ecommerce_categories",JSON.stringify(l)),{...e,categories:l};case"DELETE_CATEGORY":const a=e.categories.filter(o=>o.id!==t.payload);return localStorage.setItem("ecommerce_categories",JSON.stringify(a)),{...e,categories:a};case"TOGGLE_CATEGORY":const i=e.categories.map(o=>o.id===t.payload?{...o,active:!o.active}:o);return localStorage.setItem("ecommerce_categories",JSON.stringify(i)),{...e,categories:i};default:return e}},Ih={categories:[],loading:!1,error:null},Dh=({children:e})=>{const[t,r]=N.useReducer(Ah,Ih);N.useEffect(()=>{const h=localStorage.getItem("ecommerce_categories");h?r({type:"SET_CATEGORIES",payload:JSON.parse(h)}):(r({type:"SET_CATEGORIES",payload:nc}),localStorage.setItem("ecommerce_categories",JSON.stringify(nc)))},[]);const n=h=>{r({type:"ADD_CATEGORY",payload:h})},l=h=>{r({type:"UPDATE_CATEGORY",payload:h})},a=h=>{r({type:"DELETE_CATEGORY",payload:h})},i=h=>{r({type:"TOGGLE_CATEGORY",payload:h})},o=h=>t.categories.find(g=>g.id===parseInt(h)),c=h=>t.categories.find(g=>g.slug===h),u=()=>t.categories.filter(h=>h.active),f=()=>t.categories.map(h=>h.name),m={categories:t.categories,loading:t.loading,error:t.error,addCategory:n,updateCategory:l,deleteCategory:a,toggleCategory:i,getCategoryById:o,getCategoryBySlug:c,getActiveCategories:u,getCategoryNames:f};return s.jsx(Ld.Provider,{value:m,children:e})},hn=()=>{const e=N.useContext(Ld);if(!e)throw new Error("useCategories must be used within a CategoryProvider");return e},Md=N.createContext(),Fh={id:1,username:"admin",email:"<EMAIL>",password:"admin123",name:"Admin User",role:"admin",avatar:"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face",createdAt:"2024-01-01",lastLogin:null},$h=(e,t)=>{switch(t.type){case"SET_USER":return{...e,user:t.payload,isAuthenticated:!!t.payload};case"LOGIN_SUCCESS":const r={...t.payload,lastLogin:new Date().toISOString()};if(localStorage.setItem("ecommerce_user",JSON.stringify(r)),r.role==="admin"){const a=JSON.parse(localStorage.getItem("ecommerce_admins")||"[]").map(i=>i.id===r.id?r:i);localStorage.setItem("ecommerce_admins",JSON.stringify(a))}return{...e,user:r,isAuthenticated:!0,error:null};case"LOGIN_ERROR":return{...e,user:null,isAuthenticated:!1,error:t.payload};case"LOGOUT":return localStorage.removeItem("ecommerce_user"),{...e,user:null,isAuthenticated:!1,error:null};case"REGISTER_SUCCESS":return{...e,error:null};case"REGISTER_ERROR":return{...e,error:t.payload};case"UPDATE_PROFILE":const n={...e.user,...t.payload};if(localStorage.setItem("ecommerce_user",JSON.stringify(n)),n.role==="admin"){const a=JSON.parse(localStorage.getItem("ecommerce_admins")||"[]").map(i=>i.id===n.id?n:i);localStorage.setItem("ecommerce_admins",JSON.stringify(a))}return{...e,user:n};case"CLEAR_ERROR":return{...e,error:null};default:return e}},Uh={user:null,isAuthenticated:!1,error:null,loading:!1},Bh=({children:e})=>{const[t,r]=N.useReducer($h,Uh);N.useEffect(()=>{JSON.parse(localStorage.getItem("ecommerce_admins")||"[]").length===0&&localStorage.setItem("ecommerce_admins",JSON.stringify([Fh]));const h=localStorage.getItem("ecommerce_user");if(h)try{const g=JSON.parse(h);r({type:"SET_USER",payload:g})}catch{localStorage.removeItem("ecommerce_user")}},[]);const n=async m=>{try{const{email:h,password:g,isAdmin:j}=m;if(j){const w=JSON.parse(localStorage.getItem("ecommerce_admins")||"[]").find(p=>(p.email===h||p.username===h)&&p.password===g);return w?(r({type:"LOGIN_SUCCESS",payload:w}),{success:!0}):(r({type:"LOGIN_ERROR",payload:"Invalid admin credentials"}),{success:!1,error:"Invalid admin credentials"})}else{const w=JSON.parse(localStorage.getItem("ecommerce_customers")||"[]").find(p=>p.email===h);if(w){const p={...w,role:"customer",password:void 0};return r({type:"LOGIN_SUCCESS",payload:p}),{success:!0}}else return r({type:"LOGIN_ERROR",payload:"Customer not found"}),{success:!1,error:"Customer not found"}}}catch{return r({type:"LOGIN_ERROR",payload:"Login failed"}),{success:!1,error:"Login failed"}}},l=async m=>{try{const{email:h,password:g,firstName:j,lastName:y,isAdmin:w}=m;if(w){const p=JSON.parse(localStorage.getItem("ecommerce_admins")||"[]");if(p.find(v=>v.email===h||v.username===h))return r({type:"REGISTER_ERROR",payload:"Admin already exists"}),{success:!1,error:"Admin already exists"};const x={id:Math.max(...p.map(v=>v.id),0)+1,username:h.split("@")[0],email:h,password:g,name:`${j} ${y}`,role:"admin",avatar:`https://ui-avatars.com/api/?name=${j}+${y}&background=6366f1&color=fff`,createdAt:new Date().toISOString(),lastLogin:null};return p.push(x),localStorage.setItem("ecommerce_admins",JSON.stringify(p)),r({type:"REGISTER_SUCCESS"}),{success:!0}}else{const p=JSON.parse(localStorage.getItem("ecommerce_customers")||"[]");if(p.find(v=>v.email===h))return r({type:"REGISTER_ERROR",payload:"Customer already exists"}),{success:!1,error:"Customer already exists"};const x={id:Math.max(...p.map(v=>v.id),0)+1,firstName:j,lastName:y,email:h,phone:m.phone||"",address:m.address||"",dateJoined:new Date().toISOString().split("T")[0],totalOrders:0,totalSpent:0,status:"Active",lastOrder:null,avatar:`https://ui-avatars.com/api/?name=${j}+${y}&background=6366f1&color=fff`};return p.push(x),localStorage.setItem("ecommerce_customers",JSON.stringify(p)),r({type:"REGISTER_SUCCESS"}),{success:!0}}}catch{return r({type:"REGISTER_ERROR",payload:"Registration failed"}),{success:!1,error:"Registration failed"}}},a=()=>{r({type:"LOGOUT"})},i=m=>{r({type:"UPDATE_PROFILE",payload:m})},o=()=>{r({type:"CLEAR_ERROR"})},c=()=>{var m;return((m=t.user)==null?void 0:m.role)==="admin"},u=()=>{var m;return((m=t.user)==null?void 0:m.role)==="customer"},f={user:t.user,isAuthenticated:t.isAuthenticated,error:t.error,loading:t.loading,login:n,register:l,logout:a,updateProfile:i,clearError:o,isAdmin:c,isCustomer:u};return s.jsx(Md.Provider,{value:f,children:e})},el=()=>{const e=N.useContext(Md);if(!e)throw new Error("useAuth must be used within an AuthProvider");return e},zd=N.createContext(),Rl={appName:"YourShop",appDescription:"Your destination for premium fashion and lifestyle products",appLogo:"YS",businessName:"YourShop",email:"<EMAIL>",phone:"+****************",address:"Your Address Here, Your City, State 12345",website:"https://yourshop.com",primaryColor:"#ec4899",secondaryColor:"#6b7280",emailNotifications:!0,orderNotifications:!0,marketingEmails:!1,twoFactorAuth:!1,sessionTimeout:30},Vh=({children:e})=>{const[t,r]=N.useState(Rl);N.useEffect(()=>{const c=localStorage.getItem("ecommerce_settings");if(c)try{const u=JSON.parse(c);r(f=>({...f,...u}))}catch(u){console.error("Error loading settings:",u)}},[]);const o={settings:t,updateSettings:c=>{const u={...t,...c};return r(u),localStorage.setItem("ecommerce_settings",JSON.stringify(u)),u},updateSetting:(c,u)=>{const f={...t,[c]:u};return r(f),localStorage.setItem("ecommerce_settings",JSON.stringify(f)),f},resetSettings:()=>{r(Rl),localStorage.setItem("ecommerce_settings",JSON.stringify(Rl))},getSetting:c=>t[c]};return s.jsx(zd.Provider,{value:o,children:e})},Wh=()=>{const e=N.useContext(zd);if(!e)throw new Error("useSettings must be used within a SettingsProvider");return e};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var Hh={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Qh=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),R=(e,t)=>{const r=N.forwardRef(({color:n="currentColor",size:l=24,strokeWidth:a=2,absoluteStrokeWidth:i,className:o="",children:c,...u},f)=>N.createElement("svg",{ref:f,...Hh,width:l,height:l,stroke:n,strokeWidth:i?Number(a)*24/Number(l):a,className:["lucide",`lucide-${Qh(e)}`,o].join(" "),...u},[...t.map(([m,h])=>N.createElement(m,h)),...Array.isArray(c)?c:[c]]));return r.displayName=`${e}`,r};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Yh=R("Activity",[["path",{d:"M22 12h-4l-3 9L9 3l-3 9H2",key:"d5dnw9"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ra=R("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Jh=R("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gh=R("Award",[["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}],["path",{d:"M15.477 12.89 17 22l-5-3-5 3 1.523-9.11",key:"em7aur"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ad=R("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Kh=R("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const sc=R("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qh=R("Camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xh=R("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zh=R("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const lc=R("Crown",[["path",{d:"m2 4 3 12h14l3-12-6 7-4-7-4 7-6-7zm3 16h14",key:"zkxr6b"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Rs=R("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const e0=R("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ri=R("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const t0=R("Facebook",[["path",{d:"M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z",key:"1jg4f8"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const r0=R("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const n0=R("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const s0=R("Headphones",[["path",{d:"M3 14h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-7a9 9 0 0 1 18 0v7a2 2 0 0 1-2 2h-1a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2h3",key:"1xhozi"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Oi=R("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const l0=R("Instagram",[["rect",{width:"20",height:"20",x:"2",y:"2",rx:"5",ry:"5",key:"2e1cvw"}],["path",{d:"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z",key:"9exkf1"}],["line",{x1:"17.5",x2:"17.51",y1:"6.5",y2:"6.5",key:"r4j83e"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const a0=R("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const i0=R("LogIn",[["path",{d:"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4",key:"u53s6r"}],["polyline",{points:"10 17 15 12 10 7",key:"1ail0h"}],["line",{x1:"15",x2:"3",y1:"12",y2:"12",key:"v6grx8"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Id=R("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xn=R("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Li=R("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Dd=R("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fd=R("Minus",[["path",{d:"M5 12h14",key:"1ays0h"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gn=R("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const o0=R("Palette",[["circle",{cx:"13.5",cy:"6.5",r:".5",key:"1xcu5"}],["circle",{cx:"17.5",cy:"10.5",r:".5",key:"736e4u"}],["circle",{cx:"8.5",cy:"7.5",r:".5",key:"clrty"}],["circle",{cx:"6.5",cy:"12.5",r:".5",key:"1s4xz9"}],["path",{d:"M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z",key:"12rzf8"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const tl=R("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Os=R("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const c0=R("PieChart",[["path",{d:"M21.21 15.89A10 10 0 1 1 8 2.83",key:"k2fpak"}],["path",{d:"M22 12A10 10 0 0 0 12 2v10z",key:"1rfc4y"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Pn=R("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $d=R("RotateCcw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yn=R("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ls=R("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ud=R("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rl=R("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Oa=R("ShoppingBag",[["path",{d:"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z",key:"hou9p0"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const kr=R("ShoppingCart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bd=R("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const u0=R("Store",[["path",{d:"m2 7 4.41-4.41A2 2 0 0 1 7.83 2h8.34a2 2 0 0 1 1.42.59L22 7",key:"ztvudi"}],["path",{d:"M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8",key:"1b2hhj"}],["path",{d:"M15 22v-4a2 2 0 0 0-2-2h-2a2 2 0 0 0-2 2v4",key:"2ebpfo"}],["path",{d:"M2 7h20",key:"1fcdvo"}],["path",{d:"M22 7v3a2 2 0 0 1-2 2v0a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 16 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 12 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 8 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 4 12v0a2 2 0 0 1-2-2V7",key:"jon5kx"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ol=R("Tag",[["path",{d:"M12 2H2v10l9.29 9.29c.94.94 2.48.94 3.42 0l6.58-6.58c.94-.94.94-2.48 0-3.42L12 2Z",key:"14b2ls"}],["path",{d:"M7 7h.01",key:"7u93v4"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const d0=R("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const m0=R("ToggleLeft",[["rect",{width:"20",height:"12",x:"2",y:"6",rx:"6",ry:"6",key:"f2vt7d"}],["circle",{cx:"8",cy:"12",r:"2",key:"1nvbw3"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const f0=R("ToggleRight",[["rect",{width:"20",height:"12",x:"2",y:"6",rx:"6",ry:"6",key:"f2vt7d"}],["circle",{cx:"16",cy:"12",r:"2",key:"4ma0v8"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const nl=R("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const p0=R("TrendingDown",[["polyline",{points:"22 17 13.5 8.5 8.5 13.5 2 7",key:"1r2t7k"}],["polyline",{points:"16 17 22 17 22 11",key:"11uiuu"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vd=R("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Mi=R("Truck",[["path",{d:"M5 18H3c-.6 0-1-.4-1-1V7c0-.6.4-1 1-1h10c.6 0 1 .4 1 1v11",key:"hs4xqm"}],["path",{d:"M14 9h4l4 4v4c0 .6-.4 1-1 1h-2",key:"11fp61"}],["circle",{cx:"7",cy:"18",r:"2",key:"19iecd"}],["path",{d:"M15 18H9",key:"1lyqi6"}],["circle",{cx:"17",cy:"18",r:"2",key:"332jqn"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const h0=R("Twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ll=R("UserCheck",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["polyline",{points:"16 11 18 13 22 9",key:"1pwet4"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const x0=R("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const g0=R("UserX",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"17",x2:"22",y1:"8",y2:"13",key:"3nzzx3"}],["line",{x1:"22",x2:"17",y1:"8",y2:"13",key:"1swrse"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ms=R("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zs=R("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wt=R("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const y0=R("Youtube",[["path",{d:"M2.5 17a24.12 24.12 0 0 1 0-10 2 2 0 0 1 1.4-1.4 49.56 49.56 0 0 1 16.2 0A2 2 0 0 1 21.5 7a24.12 24.12 0 0 1 0 10 2 2 0 0 1-1.4 1.4 49.55 49.55 0 0 1-16.2 0A2 2 0 0 1 2.5 17",key:"1q2vi4"}],["path",{d:"m10 15 5-3-5-3z",key:"1jp15x"}]]),v0=({isOpen:e,onClose:t,defaultTab:r="login"})=>{const[n,l]=N.useState(r),[a,i]=N.useState(!1),[o,c]=N.useState(!1),{login:u,register:f,error:m,clearError:h}=el(),[g,j]=N.useState({email:"",password:"",firstName:"",lastName:"",phone:"",address:""}),y=x=>{const{name:v,value:k}=x.target;j(C=>({...C,[v]:k})),h()},w=async x=>{x.preventDefault(),n==="login"?(await u({email:g.email,password:g.password,isAdmin:o})).success&&(t(),p()):(await f({...g,isAdmin:o})).success&&(l("login"),p())},p=()=>{j({email:"",password:"",firstName:"",lastName:"",phone:"",address:""}),i(!1),h()},d=()=>{p(),t()};return e?s.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4",children:s.jsxs("div",{className:"bg-white rounded-xl max-w-md w-full max-h-[90vh] overflow-y-auto",children:[s.jsxs("div",{className:"p-6 border-b border-gray-200",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:n==="login"?"Sign In":"Create Account"}),s.jsx("button",{onClick:d,className:"text-gray-400 hover:text-gray-600",children:s.jsx(Wt,{className:"w-6 h-6"})})]}),s.jsxs("div",{className:"flex mt-4 bg-gray-100 rounded-lg p-1",children:[s.jsxs("button",{onClick:()=>l("login"),className:`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${n==="login"?"bg-white text-primary-600 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:[s.jsx(i0,{className:"w-4 h-4 inline mr-2"}),"Sign In"]}),s.jsxs("button",{onClick:()=>l("register"),className:`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${n==="register"?"bg-white text-primary-600 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:[s.jsx(x0,{className:"w-4 h-4 inline mr-2"}),"Sign Up"]})]})]}),s.jsxs("form",{onSubmit:w,className:"p-6 space-y-4",children:[s.jsxs("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[s.jsx("span",{className:"text-sm font-medium text-gray-700",children:n==="login"?"Admin Login":"Admin Registration"}),s.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[s.jsx("input",{type:"checkbox",checked:o,onChange:x=>c(x.target.checked),className:"sr-only peer"}),s.jsx("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"})]})]}),m&&s.jsx("div",{className:"p-3 bg-red-50 border border-red-200 rounded-lg",children:s.jsx("p",{className:"text-sm text-red-600",children:m})}),n==="register"&&s.jsxs(s.Fragment,{children:[s.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"First Name *"}),s.jsxs("div",{className:"relative",children:[s.jsx(Ms,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),s.jsx("input",{type:"text",name:"firstName",value:g.firstName,onChange:y,className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500",required:!0})]})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Last Name *"}),s.jsxs("div",{className:"relative",children:[s.jsx(Ms,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),s.jsx("input",{type:"text",name:"lastName",value:g.lastName,onChange:y,className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500",required:!0})]})]})]}),!o&&s.jsxs(s.Fragment,{children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Phone"}),s.jsx("input",{type:"tel",name:"phone",value:g.phone,onChange:y,placeholder:"+****************",className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Address"}),s.jsx("textarea",{name:"address",value:g.address,onChange:y,rows:"2",placeholder:"Street address, City, State, ZIP",className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"})]})]})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email Address *"}),s.jsxs("div",{className:"relative",children:[s.jsx(xn,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),s.jsx("input",{type:"email",name:"email",value:g.email,onChange:y,className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500",required:!0})]})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Password *"}),s.jsxs("div",{className:"relative",children:[s.jsx(a0,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),s.jsx("input",{type:a?"text":"password",name:"password",value:g.password,onChange:y,className:"w-full pl-10 pr-12 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500",required:!0}),s.jsx("button",{type:"button",onClick:()=>i(!a),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:a?s.jsx(e0,{className:"w-4 h-4"}):s.jsx(Ri,{className:"w-4 h-4"})})]})]}),n==="login"&&o&&s.jsx("div",{className:"p-3 bg-blue-50 border border-blue-200 rounded-lg",children:s.jsxs("p",{className:"text-sm text-blue-700",children:[s.jsx("strong",{children:"Default Admin:"}),s.jsx("br",{}),"Email: <EMAIL>",s.jsx("br",{}),"Password: admin123"]})}),s.jsx("button",{type:"submit",className:"w-full btn-primary text-lg py-3",children:n==="login"?"Sign In":"Create Account"}),s.jsx("div",{className:"text-center text-sm text-gray-600",children:n==="login"?s.jsxs(s.Fragment,{children:["Don't have an account?"," ",s.jsx("button",{type:"button",onClick:()=>l("register"),className:"text-primary-600 hover:text-primary-700 font-medium",children:"Sign up"})]}):s.jsxs(s.Fragment,{children:["Already have an account?"," ",s.jsx("button",{type:"button",onClick:()=>l("login"),className:"text-primary-600 hover:text-primary-700 font-medium",children:"Sign in"})]})})]})]})}):null},j0=()=>{const[e,t]=N.useState(!1),[r,n]=N.useState(!1),[l,a]=N.useState(!1),{getCartItemsCount:i}=En(),{user:o,isAuthenticated:c,logout:u,isAdmin:f}=el(),{settings:m}=Wh(),{getActiveCategories:h}=hn(),g=bn(),j=h().map(y=>({name:y.name,path:`/?category=${y.slug}`}));return s.jsxs("header",{className:"bg-white shadow-soft sticky top-0 z-50",children:[s.jsx("div",{className:"bg-primary-50 text-primary-700 text-sm py-2",children:s.jsx("div",{className:"max-w-7xl mx-auto px-4 text-center",children:"Free shipping on orders over $100 • 30-day returns"})}),s.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[s.jsxs("div",{className:"flex items-center justify-between h-16",children:[s.jsxs(X,{to:"/",className:"flex items-center space-x-2",children:[s.jsx("div",{className:"w-8 h-8 bg-gradient-to-br rounded-lg flex items-center justify-center",style:{background:`linear-gradient(to bottom right, ${m.primaryColor}, ${m.primaryColor}dd)`},children:s.jsx("span",{className:"text-white font-bold text-lg",children:m.appLogo})}),s.jsx("span",{className:"text-xl font-bold text-gray-900",children:m.appName})]}),s.jsx("nav",{className:"hidden md:flex items-center space-x-8",children:j.map(y=>s.jsx(X,{to:y.path,className:"text-gray-700 hover:text-primary-600 font-medium transition-colors",children:y.name},y.name))}),s.jsx("div",{className:"hidden md:flex flex-1 max-w-md mx-8",children:s.jsxs("div",{className:"relative w-full",children:[s.jsx(Ls,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),s.jsx("input",{type:"text",placeholder:"Search products...",className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"})]})}),s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsx("button",{className:"p-2 text-gray-700 hover:text-primary-600 transition-colors",children:s.jsx(Oi,{className:"w-5 h-5"})}),c?s.jsxs("div",{className:"relative",children:[s.jsxs("button",{onClick:()=>a(!l),className:"flex items-center space-x-2 p-2 text-gray-700 hover:text-primary-600 transition-colors",children:[s.jsx("img",{src:(o==null?void 0:o.avatar)||`https://ui-avatars.com/api/?name=${o==null?void 0:o.name}&background=6366f1&color=fff`,alt:o==null?void 0:o.name,className:"w-8 h-8 rounded-full object-cover"}),s.jsx("span",{className:"hidden md:block text-sm font-medium",children:o==null?void 0:o.name})]}),l&&s.jsxs("div",{className:"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50",children:[s.jsxs("div",{className:"px-4 py-2 border-b border-gray-200",children:[s.jsx("p",{className:"text-sm font-medium text-gray-900",children:o==null?void 0:o.name}),s.jsx("p",{className:"text-xs text-gray-500",children:o==null?void 0:o.email})]}),f()&&s.jsxs(X,{to:"/#/admin",className:"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",onClick:()=>a(!1),children:[s.jsx(Ud,{className:"w-4 h-4 mr-2"}),"Admin Dashboard"]}),s.jsxs("button",{onClick:()=>{u(),a(!1),g("/")},className:"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:[s.jsx(Id,{className:"w-4 h-4 mr-2"}),"Sign Out"]})]})]}):s.jsx("button",{onClick:()=>n(!0),className:"p-2 text-gray-700 hover:text-primary-600 transition-colors",children:s.jsx(Ms,{className:"w-5 h-5"})}),s.jsxs("button",{onClick:()=>g("/cart"),className:"relative p-2 text-gray-700 hover:text-primary-600 transition-colors",children:[s.jsx(kr,{className:"w-5 h-5"}),i()>0&&s.jsx("span",{className:"absolute -top-1 -right-1 bg-primary-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center",children:i()})]}),s.jsx("button",{onClick:()=>t(!e),className:"md:hidden p-2 text-gray-700",children:e?s.jsx(Wt,{className:"w-5 h-5"}):s.jsx(Dd,{className:"w-5 h-5"})})]})]}),e&&s.jsx("div",{className:"md:hidden py-4 border-t border-gray-200",children:s.jsxs("div",{className:"flex flex-col space-y-4",children:[s.jsxs("div",{className:"relative",children:[s.jsx(Ls,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),s.jsx("input",{type:"text",placeholder:"Search products...",className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500"})]}),j.map(y=>s.jsx(X,{to:y.path,className:"text-gray-700 hover:text-primary-600 font-medium py-2",onClick:()=>t(!1),children:y.name},y.name))]})})]}),s.jsx(v0,{isOpen:r,onClose:()=>n(!1)})]})},N0=()=>s.jsx("footer",{className:"bg-gray-900 text-white",children:s.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:[s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("div",{className:"w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-lg flex items-center justify-center",children:s.jsx("span",{className:"text-white font-bold text-lg",children:"YS"})}),s.jsx("span",{className:"text-xl font-bold",children:"YourShop"})]}),s.jsx("p",{className:"text-gray-400 text-sm",children:"Your destination for premium fashion and lifestyle products. Discover quality, style, and comfort in every piece."}),s.jsxs("div",{className:"flex space-x-4",children:[s.jsx("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:s.jsx(t0,{className:"w-5 h-5"})}),s.jsx("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:s.jsx(h0,{className:"w-5 h-5"})}),s.jsx("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:s.jsx(l0,{className:"w-5 h-5"})}),s.jsx("a",{href:"#",className:"text-gray-400 hover:text-white transition-colors",children:s.jsx(y0,{className:"w-5 h-5"})})]})]}),s.jsxs("div",{className:"space-y-4",children:[s.jsx("h3",{className:"text-lg font-semibold",children:"Quick Links"}),s.jsx("ul",{className:"space-y-2",children:[{name:"About Us",path:"/about"},{name:"Contact",path:"/contact"},{name:"Size Guide",path:"/size-guide"},{name:"Shipping Info",path:"/shipping"},{name:"Returns",path:"/returns"},{name:"FAQ",path:"/faq"}].map(e=>s.jsx("li",{children:s.jsx(X,{to:e.path,className:"text-gray-400 hover:text-white transition-colors text-sm",children:e.name})},e.name))})]}),s.jsxs("div",{className:"space-y-4",children:[s.jsx("h3",{className:"text-lg font-semibold",children:"Categories"}),s.jsx("ul",{className:"space-y-2",children:[{name:"Men's Fashion",path:"/?category=men"},{name:"Women's Fashion",path:"/?category=women"},{name:"Bags & Accessories",path:"/?category=bags"},{name:"New Arrivals",path:"/new-arrivals"},{name:"Sale Items",path:"/sale"},{name:"Gift Cards",path:"/gift-cards"}].map(e=>s.jsx("li",{children:s.jsx(X,{to:e.path,className:"text-gray-400 hover:text-white transition-colors text-sm",children:e.name})},e.name))})]}),s.jsxs("div",{className:"space-y-4",children:[s.jsx("h3",{className:"text-lg font-semibold",children:"Contact Us"}),s.jsxs("div",{className:"space-y-3",children:[s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx(Li,{className:"w-4 h-4 text-gray-400"}),s.jsxs("span",{className:"text-gray-400 text-sm",children:["Your Address Here",s.jsx("br",{}),"Your City, State 12345"]})]}),s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx(Os,{className:"w-4 h-4 text-gray-400"}),s.jsx("span",{className:"text-gray-400 text-sm",children:"+****************"})]}),s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx(xn,{className:"w-4 h-4 text-gray-400"}),s.jsx("span",{className:"text-gray-400 text-sm",children:"<EMAIL>"})]})]}),s.jsxs("div",{className:"mt-6",children:[s.jsx("h4",{className:"text-sm font-semibold mb-2",children:"Newsletter"}),s.jsxs("div",{className:"flex",children:[s.jsx("input",{type:"email",placeholder:"Your email",className:"flex-1 px-3 py-2 bg-gray-800 border border-gray-700 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-primary-500 text-sm"}),s.jsx("button",{className:"px-4 py-2 bg-primary-500 hover:bg-primary-600 rounded-r-lg transition-colors text-sm font-medium",children:"Subscribe"})]})]})]})]}),s.jsx("div",{className:"border-t border-gray-800 mt-12 pt-8",children:s.jsxs("div",{className:"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0",children:[s.jsx("p",{className:"text-gray-400 text-sm",children:"© 2024 YourShop. All rights reserved."}),s.jsxs("div",{className:"flex space-x-6",children:[s.jsx(X,{to:"/privacy",className:"text-gray-400 hover:text-white transition-colors text-sm",children:"Privacy Policy"}),s.jsx(X,{to:"/terms",className:"text-gray-400 hover:text-white transition-colors text-sm",children:"Terms of Service"}),s.jsx(X,{to:"/cookies",className:"text-gray-400 hover:text-white transition-colors text-sm",children:"Cookie Policy"})]})]})})]})}),La=({product:e})=>{const{addToCart:t}=En(),r=l=>{l.preventDefault(),l.stopPropagation(),t(e)},n=e.originalPrice?Math.round((e.originalPrice-e.price)/e.originalPrice*100):0;return s.jsx(X,{to:`/product/${e.id}`,className:"group",children:s.jsxs("div",{className:"card overflow-hidden hover:shadow-soft-lg transition-all duration-300 transform hover:-translate-y-1",children:[s.jsxs("div",{className:"relative overflow-hidden",children:[s.jsx("img",{src:e.image,alt:e.name,className:"w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300"}),n>0&&s.jsxs("div",{className:"absolute top-3 left-3 bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-full",children:["-",n,"%"]}),s.jsx("button",{className:"absolute top-3 right-3 p-2 bg-white rounded-full shadow-md opacity-0 group-hover:opacity-100 transition-opacity duration-300 hover:bg-gray-50",children:s.jsx(Oi,{className:"w-4 h-4 text-gray-600"})}),s.jsxs("button",{onClick:r,className:"absolute bottom-3 left-1/2 transform -translate-x-1/2 bg-primary-500 text-white px-4 py-2 rounded-lg opacity-0 group-hover:opacity-100 transition-all duration-300 hover:bg-primary-600 flex items-center space-x-2",children:[s.jsx(kr,{className:"w-4 h-4"}),s.jsx("span",{children:"Add to Cart"})]})]}),s.jsxs("div",{className:"p-4",children:[s.jsxs("div",{className:"flex items-center justify-between mb-2",children:[s.jsx("span",{className:"text-sm text-gray-500 font-medium",children:e.category}),s.jsxs("div",{className:"flex items-center space-x-1",children:[s.jsx(Bd,{className:"w-4 h-4 text-yellow-400 fill-current"}),s.jsx("span",{className:"text-sm text-gray-600",children:e.rating}),s.jsxs("span",{className:"text-sm text-gray-400",children:["(",e.reviews,")"]})]})]}),s.jsx("h3",{className:"font-semibold text-gray-900 mb-2 line-clamp-2 group-hover:text-primary-600 transition-colors",children:e.name}),s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsxs("span",{className:"text-lg font-bold text-gray-900",children:["$",e.price]}),e.originalPrice&&s.jsxs("span",{className:"text-sm text-gray-500 line-through",children:["$",e.originalPrice]})]}),e.colors&&e.colors.length>0&&s.jsxs("div",{className:"flex items-center space-x-2 mt-3",children:[s.jsx("span",{className:"text-xs text-gray-500",children:"Colors:"}),s.jsxs("div",{className:"flex space-x-1",children:[e.colors.slice(0,3).map((l,a)=>s.jsx("div",{className:"w-4 h-4 rounded-full border border-gray-300",style:{backgroundColor:l.toLowerCase()==="white"?"#ffffff":l.toLowerCase()==="black"?"#000000":l.toLowerCase()==="gray"?"#6b7280":l.toLowerCase()==="navy"?"#1e3a8a":l.toLowerCase()==="pink"?"#ec4899":l.toLowerCase()==="blue"?"#3b82f6":l.toLowerCase()==="brown"?"#92400e":l.toLowerCase()==="tan"?"#d2b48c":l.toLowerCase()}},a)),e.colors.length>3&&s.jsxs("span",{className:"text-xs text-gray-400",children:["+",e.colors.length-3]})]})]})]})]})})},w0=()=>{const[e]=bh(),{products:t}=Zs();hn();const[r,n]=N.useState([]),[l,a]=N.useState("all");N.useEffect(()=>{n(t)},[t]),N.useEffect(()=>{const f=e.get("category");if(f){a(f);const m=t.filter(h=>h.category.toLowerCase()===f.toLowerCase());n(m)}else a("all"),n(t)},[e,t]);const i=t.filter(f=>f.featured),{getActiveCategories:o}=hn(),c=["all",...o().map(f=>f.slug)],u=f=>{if(a(f),f==="all")n(t);else{const m=t.filter(h=>h.category.toLowerCase()===f.toLowerCase());n(m)}};return s.jsxs("div",{className:"min-h-screen",children:[s.jsx("section",{className:"relative bg-gradient-to-br from-primary-50 to-primary-100 overflow-hidden",children:s.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20",children:s.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center",children:[s.jsxs("div",{className:"space-y-8",children:[s.jsxs("div",{className:"space-y-4",children:[s.jsxs("h1",{className:"text-4xl md:text-6xl font-bold text-gray-900 leading-tight",children:["Discover Your",s.jsx("span",{className:"text-primary-600 block",children:"Perfect Style"})]}),s.jsx("p",{className:"text-lg text-gray-600 max-w-md",children:"Explore our curated collection of premium fashion items designed for the modern lifestyle."})]}),s.jsxs("div",{className:"flex flex-col sm:flex-row gap-4",children:[s.jsxs(X,{to:"/?category=women",className:"btn-primary inline-flex items-center justify-center space-x-2 text-lg px-8 py-4",children:[s.jsx("span",{children:"Shop Now"}),s.jsx(Jh,{className:"w-5 h-5"})]}),s.jsx(X,{to:"/?category=men",className:"btn-secondary inline-flex items-center justify-center text-lg px-8 py-4",children:"Explore Collection"})]})]}),s.jsx("div",{className:"relative",children:s.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[s.jsxs("div",{className:"space-y-4",children:[s.jsx("img",{src:"https://images.unsplash.com/photo-1515372039744-b8f02a3ae446?w=300&h=400&fit=crop",alt:"Fashion 1",className:"w-full h-64 object-cover rounded-2xl shadow-soft"}),s.jsx("img",{src:"https://images.unsplash.com/photo-**********-98eeb64c6a62?w=300&h=300&fit=crop",alt:"Fashion 2",className:"w-full h-48 object-cover rounded-2xl shadow-soft"})]}),s.jsxs("div",{className:"space-y-4 pt-8",children:[s.jsx("img",{src:"https://images.unsplash.com/photo-1521572163474-6864f9cf17ab?w=300&h=300&fit=crop",alt:"Fashion 3",className:"w-full h-48 object-cover rounded-2xl shadow-soft"}),s.jsx("img",{src:"https://images.unsplash.com/photo-1551028719-00167b16eac5?w=300&h=400&fit=crop",alt:"Fashion 4",className:"w-full h-64 object-cover rounded-2xl shadow-soft"})]})]})})]})})}),s.jsx("section",{className:"py-16 bg-white",children:s.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:s.jsx("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[{icon:Mi,title:"Free Shipping",desc:"On orders over $100"},{icon:rl,title:"Secure Payment",desc:"100% secure checkout"},{icon:$d,title:"Easy Returns",desc:"30-day return policy"},{icon:s0,title:"24/7 Support",desc:"Dedicated customer service"}].map((f,m)=>s.jsxs("div",{className:"text-center space-y-4",children:[s.jsx("div",{className:"w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto",children:s.jsx(f.icon,{className:"w-8 h-8 text-primary-600"})}),s.jsx("h3",{className:"font-semibold text-gray-900",children:f.title}),s.jsx("p",{className:"text-gray-600 text-sm",children:f.desc})]},m))})})}),i.length>0&&s.jsx("section",{className:"py-16 bg-gray-50",children:s.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[s.jsxs("div",{className:"text-center mb-12",children:[s.jsx("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Featured Products"}),s.jsx("p",{className:"text-gray-600 max-w-2xl mx-auto",children:"Discover our handpicked selection of trending items that define modern style."})]}),s.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8",children:i.map(f=>s.jsx(La,{product:f},f.id))})]})}),s.jsx("section",{className:"py-16 bg-white",children:s.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[s.jsxs("div",{className:"text-center mb-12",children:[s.jsx("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Shop by Category"}),s.jsx("p",{className:"text-gray-600 max-w-2xl mx-auto",children:"Browse our complete collection organized by category."})]}),s.jsx("div",{className:"flex justify-center mb-12",children:s.jsx("div",{className:"flex space-x-1 bg-gray-100 p-1 rounded-lg",children:c.map(f=>s.jsx("button",{onClick:()=>u(f),className:`px-6 py-2 rounded-md font-medium transition-colors ${l===f?"bg-white text-primary-600 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:f.charAt(0).toUpperCase()+f.slice(1)},f))})}),s.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8",children:r.map(f=>s.jsx(La,{product:f},f.id))})]})})]})},S0=()=>{const{id:e}=Xp(),{addToCart:t}=En(),{products:r,getProductById:n}=Zs(),[l,a]=N.useState(null),[i,o]=N.useState(""),[c,u]=N.useState(""),[f,m]=N.useState(1),[h,g]=N.useState([]);N.useEffect(()=>{const p=n(e);if(p){a(p),o(p.sizes[0]),u(p.colors[0]);const d=r.filter(x=>x.category===p.category&&x.id!==p.id).slice(0,4);g(d)}},[e,r,n]);const j=()=>{l&&t({...l,selectedSize:i,selectedColor:c,quantity:f})},y=p=>{m(d=>Math.max(1,d+p))};if(!l)return s.jsx("div",{className:"min-h-screen flex items-center justify-center",children:s.jsxs("div",{className:"text-center",children:[s.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Product not found"}),s.jsx(X,{to:"/",className:"btn-primary",children:"Back to Home"})]})});const w=l.originalPrice?Math.round((l.originalPrice-l.price)/l.originalPrice*100):0;return s.jsxs("div",{className:"min-h-screen bg-gray-50",children:[s.jsx("div",{className:"bg-white border-b",children:s.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4",children:s.jsxs("nav",{className:"flex items-center space-x-2 text-sm",children:[s.jsx(X,{to:"/",className:"text-gray-500 hover:text-gray-700",children:"Home"}),s.jsx("span",{className:"text-gray-400",children:"/"}),s.jsx(X,{to:`/?category=${l.category.toLowerCase()}`,className:"text-gray-500 hover:text-gray-700",children:l.category}),s.jsx("span",{className:"text-gray-400",children:"/"}),s.jsx("span",{className:"text-gray-900",children:l.name})]})})}),s.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[s.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12",children:[s.jsx("div",{className:"space-y-4",children:s.jsxs("div",{className:"relative",children:[s.jsx("img",{src:l.image,alt:l.name,className:"w-full h-96 lg:h-[600px] object-cover rounded-xl"}),w>0&&s.jsxs("div",{className:"absolute top-4 left-4 bg-red-500 text-white text-sm font-bold px-3 py-1 rounded-full",children:["-",w,"%"]}),s.jsx("button",{className:"absolute top-4 right-4 p-3 bg-white rounded-full shadow-md hover:bg-gray-50",children:s.jsx(Oi,{className:"w-5 h-5 text-gray-600"})})]})}),s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{children:[s.jsxs("div",{className:"flex items-center justify-between mb-2",children:[s.jsx("span",{className:"text-sm text-primary-600 font-medium",children:l.category}),s.jsxs("div",{className:"flex items-center space-x-1",children:[s.jsx(Bd,{className:"w-4 h-4 text-yellow-400 fill-current"}),s.jsx("span",{className:"text-sm text-gray-600",children:l.rating}),s.jsxs("span",{className:"text-sm text-gray-400",children:["(",l.reviews," reviews)"]})]})]}),s.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:l.name}),s.jsx("p",{className:"text-gray-600 leading-relaxed",children:l.description})]}),s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsxs("span",{className:"text-3xl font-bold text-gray-900",children:["$",l.price]}),l.originalPrice&&s.jsxs("span",{className:"text-xl text-gray-500 line-through",children:["$",l.originalPrice]}),w>0&&s.jsxs("span",{className:"text-sm text-green-600 font-medium",children:["Save $",(l.originalPrice-l.price).toFixed(2)]})]}),s.jsxs("div",{children:[s.jsx("h3",{className:"text-sm font-medium text-gray-900 mb-3",children:"Size"}),s.jsx("div",{className:"flex flex-wrap gap-2",children:l.sizes.map(p=>s.jsx("button",{onClick:()=>o(p),className:`px-4 py-2 border rounded-lg text-sm font-medium transition-colors ${i===p?"border-primary-500 bg-primary-50 text-primary-600":"border-gray-300 text-gray-700 hover:border-gray-400"}`,children:p},p))})]}),s.jsxs("div",{children:[s.jsx("h3",{className:"text-sm font-medium text-gray-900 mb-3",children:"Color"}),s.jsx("div",{className:"flex flex-wrap gap-2",children:l.colors.map(p=>s.jsx("button",{onClick:()=>u(p),className:`px-4 py-2 border rounded-lg text-sm font-medium transition-colors ${c===p?"border-primary-500 bg-primary-50 text-primary-600":"border-gray-300 text-gray-700 hover:border-gray-400"}`,children:p},p))})]}),s.jsxs("div",{children:[s.jsx("h3",{className:"text-sm font-medium text-gray-900 mb-3",children:"Quantity"}),s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx("button",{onClick:()=>y(-1),className:"p-2 border border-gray-300 rounded-lg hover:bg-gray-50",children:s.jsx(Fd,{className:"w-4 h-4"})}),s.jsx("span",{className:"text-lg font-medium w-12 text-center",children:f}),s.jsx("button",{onClick:()=>y(1),className:"p-2 border border-gray-300 rounded-lg hover:bg-gray-50",children:s.jsx(Pn,{className:"w-4 h-4"})})]})]}),s.jsxs("div",{className:"space-y-4",children:[s.jsxs("button",{onClick:j,className:"w-full btn-primary flex items-center justify-center space-x-2 text-lg py-4",children:[s.jsx(kr,{className:"w-5 h-5"}),s.jsxs("span",{children:["Add to Cart - $",(l.price*f).toFixed(2)]})]}),s.jsx("button",{className:"w-full btn-secondary text-lg py-4",children:"Buy Now"})]}),s.jsx("div",{className:"border-t pt-6",children:s.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-4",children:[{icon:Mi,title:"Free Shipping",desc:"On orders over $100"},{icon:rl,title:"Secure Payment",desc:"100% secure checkout"},{icon:$d,title:"Easy Returns",desc:"30-day return policy"}].map((p,d)=>s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx(p.icon,{className:"w-5 h-5 text-primary-600"}),s.jsxs("div",{children:[s.jsx("p",{className:"text-sm font-medium text-gray-900",children:p.title}),s.jsx("p",{className:"text-xs text-gray-600",children:p.desc})]})]},d))})})]})]}),h.length>0&&s.jsxs("div",{className:"mt-16",children:[s.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-8",children:"You might also like"}),s.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6",children:h.map(p=>s.jsx(La,{product:p},p.id))})]})]})]})},k0=()=>{const{items:e,updateQuantity:t,removeFromCart:r,getCartTotal:n,clearCart:l}=En(),a=bn(),i=(m,h)=>{h<=0?r(m):t(m,h)},o=n(),c=o>100?0:9.99,u=o*.08,f=o+c+u;return e.length===0?s.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:s.jsxs("div",{className:"text-center space-y-6",children:[s.jsx("div",{className:"w-24 h-24 bg-gray-200 rounded-full flex items-center justify-center mx-auto",children:s.jsx(Oa,{className:"w-12 h-12 text-gray-400"})}),s.jsxs("div",{children:[s.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Your cart is empty"}),s.jsx("p",{className:"text-gray-600",children:"Add some items to get started!"})]}),s.jsxs(X,{to:"/",className:"btn-primary inline-flex items-center space-x-2",children:[s.jsx(Ra,{className:"w-4 h-4"}),s.jsx("span",{children:"Continue Shopping"})]})]})}):s.jsx("div",{className:"min-h-screen bg-gray-50",children:s.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[s.jsxs("div",{className:"flex items-center justify-between mb-8",children:[s.jsxs("div",{children:[s.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Shopping Cart"}),s.jsxs("p",{className:"text-gray-600 mt-1",children:[e.length," item",e.length!==1?"s":""," in your cart"]})]}),s.jsxs(X,{to:"/",className:"btn-secondary flex items-center space-x-2",children:[s.jsx(Ra,{className:"w-4 h-4"}),s.jsx("span",{children:"Continue Shopping"})]})]}),s.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[s.jsxs("div",{className:"lg:col-span-2 space-y-4",children:[e.map(m=>s.jsx("div",{className:"card p-6",children:s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsx(X,{to:`/product/${m.id}`,className:"flex-shrink-0",children:s.jsx("img",{src:m.image,alt:m.name,className:"w-20 h-20 object-cover rounded-lg"})}),s.jsxs("div",{className:"flex-1 min-w-0",children:[s.jsx(X,{to:`/product/${m.id}`,className:"block",children:s.jsx("h3",{className:"text-lg font-medium text-gray-900 hover:text-primary-600 transition-colors",children:m.name})}),s.jsx("p",{className:"text-sm text-gray-500 mt-1",children:m.category}),s.jsxs("div",{className:"flex items-center space-x-4 mt-2",children:[m.selectedSize&&s.jsxs("span",{className:"text-sm text-gray-600",children:["Size: ",m.selectedSize]}),m.selectedColor&&s.jsxs("span",{className:"text-sm text-gray-600",children:["Color: ",m.selectedColor]})]})]}),s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx("button",{onClick:()=>i(m.id,m.quantity-1),className:"p-1 border border-gray-300 rounded hover:bg-gray-50",children:s.jsx(Fd,{className:"w-4 h-4"})}),s.jsx("span",{className:"text-lg font-medium w-8 text-center",children:m.quantity}),s.jsx("button",{onClick:()=>i(m.id,m.quantity+1),className:"p-1 border border-gray-300 rounded hover:bg-gray-50",children:s.jsx(Pn,{className:"w-4 h-4"})})]}),s.jsxs("div",{className:"text-right",children:[s.jsxs("p",{className:"text-lg font-bold text-gray-900",children:["$",(m.price*m.quantity).toFixed(2)]}),s.jsxs("p",{className:"text-sm text-gray-500",children:["$",m.price," each"]})]}),s.jsx("button",{onClick:()=>r(m.id),className:"p-2 text-gray-400 hover:text-red-500 transition-colors",children:s.jsx(nl,{className:"w-5 h-5"})})]})},`${m.id}-${m.selectedSize}-${m.selectedColor}`)),s.jsx("div",{className:"flex justify-end",children:s.jsx("button",{onClick:l,className:"text-sm text-gray-500 hover:text-red-500 transition-colors",children:"Clear all items"})})]}),s.jsx("div",{className:"lg:col-span-1",children:s.jsxs("div",{className:"card p-6 sticky top-8",children:[s.jsx("h2",{className:"text-xl font-bold text-gray-900 mb-6",children:"Order Summary"}),s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-gray-600",children:"Subtotal"}),s.jsxs("span",{className:"font-medium",children:["$",o.toFixed(2)]})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-gray-600",children:"Shipping"}),s.jsx("span",{className:"font-medium",children:c===0?s.jsx("span",{className:"text-green-600",children:"Free"}):`$${c.toFixed(2)}`})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-gray-600",children:"Tax"}),s.jsxs("span",{className:"font-medium",children:["$",u.toFixed(2)]})]}),s.jsx("div",{className:"border-t pt-4",children:s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-lg font-bold text-gray-900",children:"Total"}),s.jsxs("span",{className:"text-lg font-bold text-gray-900",children:["$",f.toFixed(2)]})]})})]}),c>0&&s.jsx("div",{className:"mt-4 p-3 bg-blue-50 rounded-lg",children:s.jsxs("p",{className:"text-sm text-blue-700",children:["Add $",(100-o).toFixed(2)," more for free shipping!"]})}),s.jsx("button",{onClick:()=>a("/checkout"),className:"w-full btn-primary mt-6 text-lg py-4",children:"Proceed to Checkout"}),s.jsx("div",{className:"mt-4 text-center",children:s.jsx("p",{className:"text-xs text-gray-500",children:"Secure checkout powered by SSL encryption"})})]})})]})]})})},C0=()=>{const{items:e,getCartTotal:t,clearCart:r}=En(),n=bn(),[l,a]=N.useState(1),[i,o]=N.useState({firstName:"",lastName:"",email:"",phone:"",address:"",city:"",state:"",zipCode:"",country:"United States",cardNumber:"",expiryDate:"",cvv:"",cardName:"",billingDifferent:!1,billingAddress:"",billingCity:"",billingState:"",billingZipCode:""}),c=t(),u=c>100?0:9.99,f=c*.08,m=c+u+f,h=y=>{const{name:w,value:p,type:d,checked:x}=y.target;o(v=>({...v,[w]:d==="checkbox"?x:p}))},g=y=>{y.preventDefault(),r(),n("/order-confirmation")},j=[{id:1,name:"Shipping",icon:Mi},{id:2,name:"Payment",icon:Zh},{id:3,name:"Review",icon:Xh}];return e.length===0?s.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:s.jsxs("div",{className:"text-center space-y-6",children:[s.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:"Your cart is empty"}),s.jsx("p",{className:"text-gray-600",children:"Add some items before checking out."}),s.jsx(X,{to:"/",className:"btn-primary",children:"Continue Shopping"})]})}):s.jsx("div",{className:"min-h-screen bg-gray-50",children:s.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[s.jsxs("div",{className:"flex items-center justify-between mb-8",children:[s.jsxs("div",{children:[s.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Checkout"}),s.jsx("p",{className:"text-gray-600 mt-1",children:"Complete your purchase"})]}),s.jsxs(X,{to:"/cart",className:"btn-secondary flex items-center space-x-2",children:[s.jsx(Ra,{className:"w-4 h-4"}),s.jsx("span",{children:"Back to Cart"})]})]}),s.jsx("div",{className:"mb-8",children:s.jsx("div",{className:"flex items-center justify-center space-x-8",children:j.map((y,w)=>s.jsxs("div",{className:"flex items-center",children:[s.jsx("div",{className:`flex items-center justify-center w-10 h-10 rounded-full ${l>=y.id?"bg-primary-500 text-white":"bg-gray-200 text-gray-500"}`,children:s.jsx(y.icon,{className:"w-5 h-5"})}),s.jsx("span",{className:`ml-2 text-sm font-medium ${l>=y.id?"text-primary-600":"text-gray-500"}`,children:y.name}),w<j.length-1&&s.jsx("div",{className:`w-16 h-0.5 ml-4 ${l>y.id?"bg-primary-500":"bg-gray-200"}`})]},y.id))})}),s.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[s.jsx("div",{className:"lg:col-span-2",children:s.jsxs("form",{onSubmit:g,className:"space-y-6",children:[l===1&&s.jsxs("div",{className:"card p-6",children:[s.jsx("h2",{className:"text-xl font-bold text-gray-900 mb-6",children:"Shipping Information"}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"First Name *"}),s.jsx("input",{type:"text",name:"firstName",value:i.firstName,onChange:h,className:"input",required:!0})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Last Name *"}),s.jsx("input",{type:"text",name:"lastName",value:i.lastName,onChange:h,className:"input",required:!0})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email *"}),s.jsx("input",{type:"email",name:"email",value:i.email,onChange:h,className:"input",required:!0})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Phone"}),s.jsx("input",{type:"tel",name:"phone",value:i.phone,onChange:h,className:"input"})]}),s.jsxs("div",{className:"md:col-span-2",children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Address *"}),s.jsx("input",{type:"text",name:"address",value:i.address,onChange:h,className:"input",required:!0})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"City *"}),s.jsx("input",{type:"text",name:"city",value:i.city,onChange:h,className:"input",required:!0})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"State *"}),s.jsx("input",{type:"text",name:"state",value:i.state,onChange:h,className:"input",required:!0})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"ZIP Code *"}),s.jsx("input",{type:"text",name:"zipCode",value:i.zipCode,onChange:h,className:"input",required:!0})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Country *"}),s.jsxs("select",{name:"country",value:i.country,onChange:h,className:"input",required:!0,children:[s.jsx("option",{value:"United States",children:"United States"}),s.jsx("option",{value:"Canada",children:"Canada"}),s.jsx("option",{value:"United Kingdom",children:"United Kingdom"})]})]})]}),s.jsx("div",{className:"flex justify-end mt-6",children:s.jsx("button",{type:"button",onClick:()=>a(2),className:"btn-primary",children:"Continue to Payment"})})]}),l===2&&s.jsxs("div",{className:"card p-6",children:[s.jsx("h2",{className:"text-xl font-bold text-gray-900 mb-6",children:"Payment Information"}),s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Card Number *"}),s.jsx("input",{type:"text",name:"cardNumber",value:i.cardNumber,onChange:h,placeholder:"1234 5678 9012 3456",className:"input",required:!0})]}),s.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Expiry Date *"}),s.jsx("input",{type:"text",name:"expiryDate",value:i.expiryDate,onChange:h,placeholder:"MM/YY",className:"input",required:!0})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"CVV *"}),s.jsx("input",{type:"text",name:"cvv",value:i.cvv,onChange:h,placeholder:"123",className:"input",required:!0})]})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Name on Card *"}),s.jsx("input",{type:"text",name:"cardName",value:i.cardName,onChange:h,className:"input",required:!0})]})]}),s.jsxs("div",{className:"flex justify-between mt-6",children:[s.jsx("button",{type:"button",onClick:()=>a(1),className:"btn-secondary",children:"Back to Shipping"}),s.jsx("button",{type:"button",onClick:()=>a(3),className:"btn-primary",children:"Review Order"})]})]}),l===3&&s.jsxs("div",{className:"card p-6",children:[s.jsx("h2",{className:"text-xl font-bold text-gray-900 mb-6",children:"Review Your Order"}),s.jsx("div",{className:"space-y-4 mb-6",children:e.map(y=>s.jsxs("div",{className:"flex items-center space-x-4 py-4 border-b",children:[s.jsx("img",{src:y.image,alt:y.name,className:"w-16 h-16 object-cover rounded-lg"}),s.jsxs("div",{className:"flex-1",children:[s.jsx("h3",{className:"font-medium text-gray-900",children:y.name}),s.jsxs("p",{className:"text-sm text-gray-500",children:[y.selectedSize&&`Size: ${y.selectedSize}`,y.selectedSize&&y.selectedColor&&" • ",y.selectedColor&&`Color: ${y.selectedColor}`]}),s.jsxs("p",{className:"text-sm text-gray-500",children:["Qty: ",y.quantity]})]}),s.jsxs("p",{className:"font-medium",children:["$",(y.price*y.quantity).toFixed(2)]})]},`${y.id}-${y.selectedSize}-${y.selectedColor}`))}),s.jsxs("div",{className:"flex justify-between mt-6",children:[s.jsx("button",{type:"button",onClick:()=>a(2),className:"btn-secondary",children:"Back to Payment"}),s.jsxs("button",{type:"submit",className:"btn-primary flex items-center space-x-2",children:[s.jsx(rl,{className:"w-4 h-4"}),s.jsxs("span",{children:["Place Order - $",m.toFixed(2)]})]})]})]})]})}),s.jsx("div",{className:"lg:col-span-1",children:s.jsxs("div",{className:"card p-6 sticky top-8",children:[s.jsx("h2",{className:"text-xl font-bold text-gray-900 mb-6",children:"Order Summary"}),s.jsx("div",{className:"space-y-4 mb-6",children:e.map(y=>s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx("img",{src:y.image,alt:y.name,className:"w-12 h-12 object-cover rounded-lg"}),s.jsxs("div",{className:"flex-1 min-w-0",children:[s.jsx("p",{className:"text-sm font-medium text-gray-900 truncate",children:y.name}),s.jsxs("p",{className:"text-xs text-gray-500",children:["Qty: ",y.quantity]})]}),s.jsxs("p",{className:"text-sm font-medium",children:["$",(y.price*y.quantity).toFixed(2)]})]},`${y.id}-${y.selectedSize}-${y.selectedColor}`))}),s.jsxs("div",{className:"space-y-2 border-t pt-4",children:[s.jsxs("div",{className:"flex justify-between text-sm",children:[s.jsx("span",{className:"text-gray-600",children:"Subtotal"}),s.jsxs("span",{children:["$",c.toFixed(2)]})]}),s.jsxs("div",{className:"flex justify-between text-sm",children:[s.jsx("span",{className:"text-gray-600",children:"Shipping"}),s.jsx("span",{children:u===0?"Free":`$${u.toFixed(2)}`})]}),s.jsxs("div",{className:"flex justify-between text-sm",children:[s.jsx("span",{className:"text-gray-600",children:"Tax"}),s.jsxs("span",{children:["$",f.toFixed(2)]})]}),s.jsxs("div",{className:"flex justify-between text-lg font-bold border-t pt-2",children:[s.jsx("span",{children:"Total"}),s.jsxs("span",{children:["$",m.toFixed(2)]})]})]})]})})]})]})})},b0=()=>{const{products:e,addProduct:t,updateProduct:r,deleteProduct:n}=Zs(),{getCategoryNames:l}=hn(),[a,i]=N.useState(!1),[o,c]=N.useState(null),[u,f]=N.useState({name:"",price:"",originalPrice:"",category:"Men",image:"",description:"",sizes:["S","M","L","XL"],colors:["Black","White"],rating:4.5,reviews:0,featured:!1}),m=l(),h=d=>{const{name:x,value:v,type:k,checked:C}=d.target;f(E=>({...E,[x]:k==="checkbox"?C:v}))},g=(d,x)=>{const v=x.split(",").map(k=>k.trim()).filter(k=>k);f(k=>({...k,[d]:v}))},j=d=>{d.preventDefault();const x={...u,price:parseFloat(u.price),originalPrice:u.originalPrice?parseFloat(u.originalPrice):null,rating:parseFloat(u.rating),reviews:parseInt(u.reviews)};o?r({...x,id:o.id}):t(x),y()},y=()=>{f({name:"",price:"",originalPrice:"",category:"Men",image:"",description:"",sizes:["S","M","L","XL"],colors:["Black","White"],rating:4.5,reviews:0,featured:!1}),c(null),i(!1)},w=d=>{f({name:d.name,price:d.price.toString(),originalPrice:d.originalPrice?d.originalPrice.toString():"",category:d.category,image:d.image,description:d.description,sizes:d.sizes,colors:d.colors,rating:d.rating,reviews:d.reviews,featured:d.featured}),c(d),i(!0)},p=d=>{window.confirm("Are you sure you want to delete this product?")&&n(d)};return s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx("h2",{className:"text-2xl font-bold",children:"Product Management"}),s.jsxs("button",{onClick:()=>i(!0),className:"bg-primary-500 hover:bg-primary-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors",children:[s.jsx(Pn,{className:"w-4 h-4"}),s.jsx("span",{children:"Add Product"})]})]}),a&&s.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4",children:s.jsxs("div",{className:"bg-gray-800 rounded-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:[s.jsx("div",{className:"p-6 border-b border-gray-700",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx("h3",{className:"text-xl font-bold",children:o?"Edit Product":"Add New Product"}),s.jsx("button",{onClick:y,className:"text-gray-400 hover:text-white",children:s.jsx(Wt,{className:"w-6 h-6"})})]})}),s.jsxs("form",{onSubmit:j,className:"p-6 space-y-4",children:[s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Product Name *"}),s.jsx("input",{type:"text",name:"name",value:u.name,onChange:h,className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500",required:!0})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Category *"}),s.jsx("select",{name:"category",value:u.category,onChange:h,className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500",required:!0,children:m.map(d=>s.jsx("option",{value:d,children:d},d))})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Price *"}),s.jsx("input",{type:"number",step:"0.01",name:"price",value:u.price,onChange:h,className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500",required:!0})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Original Price"}),s.jsx("input",{type:"number",step:"0.01",name:"originalPrice",value:u.originalPrice,onChange:h,className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500"})]})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Image URL *"}),s.jsx("input",{type:"url",name:"image",value:u.image,onChange:h,placeholder:"https://example.com/image.jpg",className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500",required:!0})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Description *"}),s.jsx("textarea",{name:"description",value:u.description,onChange:h,rows:"3",className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500",required:!0})]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Sizes (comma separated)"}),s.jsx("input",{type:"text",value:u.sizes.join(", "),onChange:d=>g("sizes",d.target.value),placeholder:"S, M, L, XL",className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500"})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Colors (comma separated)"}),s.jsx("input",{type:"text",value:u.colors.join(", "),onChange:d=>g("colors",d.target.value),placeholder:"Black, White, Blue",className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500"})]})]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Rating"}),s.jsx("input",{type:"number",step:"0.1",min:"0",max:"5",name:"rating",value:u.rating,onChange:h,className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500"})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Reviews Count"}),s.jsx("input",{type:"number",min:"0",name:"reviews",value:u.reviews,onChange:h,className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500"})]}),s.jsx("div",{className:"flex items-center",children:s.jsxs("label",{className:"flex items-center space-x-2 text-sm text-gray-300",children:[s.jsx("input",{type:"checkbox",name:"featured",checked:u.featured,onChange:h,className:"rounded border-gray-600 text-primary-500 focus:ring-primary-500"}),s.jsx("span",{children:"Featured Product"})]})})]}),s.jsxs("div",{className:"flex justify-end space-x-3 pt-4",children:[s.jsx("button",{type:"button",onClick:y,className:"px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors",children:"Cancel"}),s.jsxs("button",{type:"submit",className:"px-4 py-2 bg-primary-500 hover:bg-primary-600 text-white rounded-lg flex items-center space-x-2 transition-colors",children:[s.jsx(yn,{className:"w-4 h-4"}),s.jsxs("span",{children:[o?"Update":"Add"," Product"]})]})]})]})]})}),s.jsx("div",{className:"bg-gray-800 rounded-xl border border-gray-700 overflow-hidden",children:s.jsx("div",{className:"overflow-x-auto",children:s.jsxs("table",{className:"w-full",children:[s.jsx("thead",{className:"bg-gray-700",children:s.jsxs("tr",{children:[s.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider",children:"Product"}),s.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider",children:"Category"}),s.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider",children:"Price"}),s.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider",children:"Rating"}),s.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider",children:"Featured"}),s.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider",children:"Actions"})]})}),s.jsx("tbody",{className:"divide-y divide-gray-700",children:e.map(d=>s.jsxs("tr",{className:"hover:bg-gray-700",children:[s.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:s.jsxs("div",{className:"flex items-center",children:[s.jsx("img",{src:d.image,alt:d.name,className:"w-12 h-12 object-cover rounded-lg mr-4"}),s.jsxs("div",{children:[s.jsx("div",{className:"text-sm font-medium text-white",children:d.name}),s.jsxs("div",{className:"text-sm text-gray-400",children:[d.reviews," reviews"]})]})]})}),s.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-300",children:d.category}),s.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-300",children:[s.jsxs("div",{children:["$",d.price]}),d.originalPrice&&s.jsxs("div",{className:"text-xs text-gray-500 line-through",children:["$",d.originalPrice]})]}),s.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-300",children:[d.rating,"/5"]}),s.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:s.jsx("span",{className:`inline-flex px-2 py-1 text-xs rounded-full ${d.featured?"bg-green-900 text-green-300":"bg-gray-600 text-gray-300"}`,children:d.featured?"Yes":"No"})}),s.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:s.jsxs("div",{className:"flex space-x-2",children:[s.jsx("button",{onClick:()=>w(d),className:"text-blue-400 hover:text-blue-300",children:s.jsx(tl,{className:"w-4 h-4"})}),s.jsx("button",{onClick:()=>p(d.id),className:"text-red-400 hover:text-red-300",children:s.jsx(nl,{className:"w-4 h-4"})})]})})]},d.id))})]})})})]})},E0=()=>{const{customers:e,addCustomer:t,updateCustomer:r,deleteCustomer:n,getCustomerStats:l,getCustomersByStatus:a}=Od(),[i,o]=N.useState(!1),[c,u]=N.useState(null),[f,m]=N.useState(""),[h,g]=N.useState("all"),[j,y]=N.useState(null),[w,p]=N.useState({firstName:"",lastName:"",email:"",phone:"",address:"",avatar:""}),d=l(),x=a(h).filter(_=>`${_.firstName} ${_.lastName} ${_.email}`.toLowerCase().includes(f.toLowerCase())),v=_=>{const{name:Ve,value:We}=_.target;p(Rt=>({...Rt,[Ve]:We}))},k=_=>{_.preventDefault(),c?r({...w,id:c.id}):t(w),C()},C=()=>{p({firstName:"",lastName:"",email:"",phone:"",address:"",avatar:""}),u(null),o(!1)},E=_=>{p({firstName:_.firstName,lastName:_.lastName,email:_.email,phone:_.phone,address:_.address,avatar:_.avatar||""}),u(_),o(!0)},T=_=>{window.confirm("Are you sure you want to delete this customer?")&&n(_)},F=_=>{switch(_){case"Active":return"bg-green-900 text-green-300";case"VIP":return"bg-purple-900 text-purple-300";case"Inactive":return"bg-gray-600 text-gray-300";default:return"bg-gray-600 text-gray-300"}},M=_=>{switch(_){case"Active":return s.jsx(Ll,{className:"w-4 h-4"});case"VIP":return s.jsx(lc,{className:"w-4 h-4"});case"Inactive":return s.jsx(g0,{className:"w-4 h-4"});default:return s.jsx(Ll,{className:"w-4 h-4"})}};return s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("h2",{className:"text-2xl font-bold",children:"Customer Management"}),s.jsx("p",{className:"text-gray-400 mt-1",children:"Manage your customer database"})]}),s.jsxs("button",{onClick:()=>o(!0),className:"bg-primary-500 hover:bg-primary-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors",children:[s.jsx(Pn,{className:"w-4 h-4"}),s.jsx("span",{children:"Add Customer"})]})]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[s.jsx("div",{className:"bg-gray-800 rounded-xl p-6 border border-gray-700",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-gray-400 text-sm",children:"Total Customers"}),s.jsx("p",{className:"text-2xl font-bold mt-1",children:d.total})]}),s.jsx("div",{className:"p-3 rounded-lg bg-blue-900 text-blue-300",children:s.jsx(Ll,{className:"w-6 h-6"})})]})}),s.jsx("div",{className:"bg-gray-800 rounded-xl p-6 border border-gray-700",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-gray-400 text-sm",children:"VIP Customers"}),s.jsx("p",{className:"text-2xl font-bold mt-1",children:d.vip})]}),s.jsx("div",{className:"p-3 rounded-lg bg-purple-900 text-purple-300",children:s.jsx(lc,{className:"w-6 h-6"})})]})}),s.jsx("div",{className:"bg-gray-800 rounded-xl p-6 border border-gray-700",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-gray-400 text-sm",children:"Total Revenue"}),s.jsxs("p",{className:"text-2xl font-bold mt-1",children:["$",d.totalRevenue.toFixed(2)]})]}),s.jsx("div",{className:"p-3 rounded-lg bg-green-900 text-green-300",children:s.jsx(Rs,{className:"w-6 h-6"})})]})}),s.jsx("div",{className:"bg-gray-800 rounded-xl p-6 border border-gray-700",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-gray-400 text-sm",children:"Avg Order Value"}),s.jsxs("p",{className:"text-2xl font-bold mt-1",children:["$",d.avgOrderValue.toFixed(2)]})]}),s.jsx("div",{className:"p-3 rounded-lg bg-orange-900 text-orange-300",children:s.jsx(Oa,{className:"w-6 h-6"})})]})})]}),s.jsxs("div",{className:"flex flex-col sm:flex-row gap-4",children:[s.jsxs("div",{className:"relative flex-1",children:[s.jsx(Ls,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),s.jsx("input",{type:"text",placeholder:"Search customers...",value:f,onChange:_=>m(_.target.value),className:"w-full pl-10 pr-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500"})]}),s.jsxs("div",{className:"relative",children:[s.jsx(r0,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),s.jsxs("select",{value:h,onChange:_=>g(_.target.value),className:"pl-10 pr-8 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500",children:[s.jsx("option",{value:"all",children:"All Status"}),s.jsx("option",{value:"active",children:"Active"}),s.jsx("option",{value:"vip",children:"VIP"}),s.jsx("option",{value:"inactive",children:"Inactive"})]})]})]}),i&&s.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4",children:s.jsxs("div",{className:"bg-gray-800 rounded-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:[s.jsx("div",{className:"p-6 border-b border-gray-700",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx("h3",{className:"text-xl font-bold",children:c?"Edit Customer":"Add New Customer"}),s.jsx("button",{onClick:C,className:"text-gray-400 hover:text-white",children:s.jsx(Wt,{className:"w-6 h-6"})})]})}),s.jsxs("form",{onSubmit:k,className:"p-6 space-y-4",children:[s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"First Name *"}),s.jsx("input",{type:"text",name:"firstName",value:w.firstName,onChange:v,className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500",required:!0})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Last Name *"}),s.jsx("input",{type:"text",name:"lastName",value:w.lastName,onChange:v,className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500",required:!0})]})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Email Address *"}),s.jsx("input",{type:"email",name:"email",value:w.email,onChange:v,className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500",required:!0})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Phone Number"}),s.jsx("input",{type:"tel",name:"phone",value:w.phone,onChange:v,placeholder:"+****************",className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500"})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Address"}),s.jsx("textarea",{name:"address",value:w.address,onChange:v,rows:"2",placeholder:"Street address, City, State, ZIP",className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500"})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Avatar URL"}),s.jsx("input",{type:"url",name:"avatar",value:w.avatar,onChange:v,placeholder:"https://example.com/avatar.jpg",className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500"})]}),s.jsxs("div",{className:"flex justify-end space-x-3 pt-4",children:[s.jsx("button",{type:"button",onClick:C,className:"px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors",children:"Cancel"}),s.jsxs("button",{type:"submit",className:"px-4 py-2 bg-primary-500 hover:bg-primary-600 text-white rounded-lg flex items-center space-x-2 transition-colors",children:[s.jsx(yn,{className:"w-4 h-4"}),s.jsxs("span",{children:[c?"Update":"Add"," Customer"]})]})]})]})]})}),s.jsx("div",{className:"bg-gray-800 rounded-xl border border-gray-700 overflow-hidden",children:s.jsx("div",{className:"overflow-x-auto",children:s.jsxs("table",{className:"w-full",children:[s.jsx("thead",{className:"bg-gray-700",children:s.jsxs("tr",{children:[s.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider",children:"Customer"}),s.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider",children:"Contact"}),s.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider",children:"Orders"}),s.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider",children:"Total Spent"}),s.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider",children:"Status"}),s.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider",children:"Actions"})]})}),s.jsx("tbody",{className:"divide-y divide-gray-700",children:x.map(_=>s.jsxs("tr",{className:"hover:bg-gray-700",children:[s.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:s.jsxs("div",{className:"flex items-center",children:[s.jsx("img",{src:_.avatar||`https://ui-avatars.com/api/?name=${_.firstName}+${_.lastName}&background=6366f1&color=fff`,alt:`${_.firstName} ${_.lastName}`,className:"w-10 h-10 object-cover rounded-full mr-4"}),s.jsxs("div",{children:[s.jsxs("div",{className:"text-sm font-medium text-white",children:[_.firstName," ",_.lastName]}),s.jsxs("div",{className:"text-sm text-gray-400",children:["Joined ",new Date(_.dateJoined).toLocaleDateString()]})]})]})}),s.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:s.jsxs("div",{className:"text-sm text-gray-300",children:[s.jsxs("div",{className:"flex items-center mb-1",children:[s.jsx(xn,{className:"w-3 h-3 mr-1"}),_.email]}),_.phone&&s.jsxs("div",{className:"flex items-center",children:[s.jsx(Os,{className:"w-3 h-3 mr-1"}),_.phone]})]})}),s.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-300",children:[s.jsxs("div",{children:[_.totalOrders," orders"]}),_.lastOrder&&s.jsxs("div",{className:"text-xs text-gray-500",children:["Last: ",new Date(_.lastOrder).toLocaleDateString()]})]}),s.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-green-400",children:["$",_.totalSpent.toFixed(2)]}),s.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:s.jsxs("span",{className:`inline-flex items-center px-2 py-1 text-xs rounded-full ${F(_.status)}`,children:[M(_.status),s.jsx("span",{className:"ml-1",children:_.status})]})}),s.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:s.jsxs("div",{className:"flex space-x-2",children:[s.jsx("button",{onClick:()=>y(_),className:"text-blue-400 hover:text-blue-300",title:"View Details",children:s.jsx(Ri,{className:"w-4 h-4"})}),s.jsx("button",{onClick:()=>E(_),className:"text-yellow-400 hover:text-yellow-300",title:"Edit Customer",children:s.jsx(tl,{className:"w-4 h-4"})}),s.jsx("button",{onClick:()=>T(_.id),className:"text-red-400 hover:text-red-300",title:"Delete Customer",children:s.jsx(nl,{className:"w-4 h-4"})})]})})]},_.id))})]})})}),j&&s.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4",children:s.jsxs("div",{className:"bg-gray-800 rounded-xl max-w-2xl w-full",children:[s.jsx("div",{className:"p-6 border-b border-gray-700",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx("h3",{className:"text-xl font-bold",children:"Customer Details"}),s.jsx("button",{onClick:()=>y(null),className:"text-gray-400 hover:text-white",children:s.jsx(Wt,{className:"w-6 h-6"})})]})}),s.jsxs("div",{className:"p-6",children:[s.jsxs("div",{className:"flex items-center mb-6",children:[s.jsx("img",{src:j.avatar||`https://ui-avatars.com/api/?name=${j.firstName}+${j.lastName}&background=6366f1&color=fff`,alt:`${j.firstName} ${j.lastName}`,className:"w-16 h-16 object-cover rounded-full mr-4"}),s.jsxs("div",{children:[s.jsxs("h4",{className:"text-xl font-bold text-white",children:[j.firstName," ",j.lastName]}),s.jsxs("span",{className:`inline-flex items-center px-2 py-1 text-xs rounded-full ${F(j.status)}`,children:[M(j.status),s.jsx("span",{className:"ml-1",children:j.status})]})]})]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[s.jsx("div",{className:"space-y-4",children:s.jsxs("div",{children:[s.jsx("h5",{className:"text-sm font-medium text-gray-400 mb-1",children:"Contact Information"}),s.jsxs("div",{className:"space-y-2",children:[s.jsxs("div",{className:"flex items-center text-sm text-gray-300",children:[s.jsx(xn,{className:"w-4 h-4 mr-2"}),j.email]}),j.phone&&s.jsxs("div",{className:"flex items-center text-sm text-gray-300",children:[s.jsx(Os,{className:"w-4 h-4 mr-2"}),j.phone]}),j.address&&s.jsxs("div",{className:"flex items-start text-sm text-gray-300",children:[s.jsx(Li,{className:"w-4 h-4 mr-2 mt-0.5"}),j.address]})]})]})}),s.jsx("div",{className:"space-y-4",children:s.jsxs("div",{children:[s.jsx("h5",{className:"text-sm font-medium text-gray-400 mb-1",children:"Order Statistics"}),s.jsxs("div",{className:"space-y-2",children:[s.jsxs("div",{className:"flex items-center text-sm text-gray-300",children:[s.jsx(Oa,{className:"w-4 h-4 mr-2"}),j.totalOrders," total orders"]}),s.jsxs("div",{className:"flex items-center text-sm text-gray-300",children:[s.jsx(Rs,{className:"w-4 h-4 mr-2"}),"$",j.totalSpent.toFixed(2)," total spent"]}),s.jsxs("div",{className:"flex items-center text-sm text-gray-300",children:[s.jsx(sc,{className:"w-4 h-4 mr-2"}),"Joined ",new Date(j.dateJoined).toLocaleDateString()]}),j.lastOrder&&s.jsxs("div",{className:"flex items-center text-sm text-gray-300",children:[s.jsx(sc,{className:"w-4 h-4 mr-2"}),"Last order: ",new Date(j.lastOrder).toLocaleDateString()]})]})]})})]})]})]})})]})},P0=()=>{const{categories:e,addCategory:t,updateCategory:r,deleteCategory:n,toggleCategory:l}=hn(),[a,i]=N.useState(!1),[o,c]=N.useState(null),[u,f]=N.useState(""),[m,h]=N.useState({name:"",description:""}),g=e.filter(v=>v.name.toLowerCase().includes(u.toLowerCase())||v.description.toLowerCase().includes(u.toLowerCase())),j=v=>{const{name:k,value:C}=v.target;h(E=>({...E,[k]:C}))},y=v=>{v.preventDefault(),o?r({...m,id:o.id,active:o.active}):t(m),w()},w=()=>{h({name:"",description:""}),c(null),i(!1)},p=v=>{h({name:v.name,description:v.description}),c(v),i(!0)},d=v=>{window.confirm("Are you sure you want to delete this category? This action cannot be undone.")&&n(v)},x=v=>{l(v)};return s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("h2",{className:"text-2xl font-bold",children:"Category Management"}),s.jsx("p",{className:"text-gray-400 mt-1",children:"Manage product categories for your store"})]}),s.jsxs("button",{onClick:()=>i(!0),className:"bg-primary-500 hover:bg-primary-600 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors",children:[s.jsx(Pn,{className:"w-4 h-4"}),s.jsx("span",{children:"Add Category"})]})]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[s.jsx("div",{className:"bg-gray-800 rounded-xl p-6 border border-gray-700",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-gray-400 text-sm",children:"Total Categories"}),s.jsx("p",{className:"text-2xl font-bold mt-1",children:e.length})]}),s.jsx("div",{className:"p-3 rounded-lg bg-blue-900 text-blue-300",children:s.jsx(Ol,{className:"w-6 h-6"})})]})}),s.jsx("div",{className:"bg-gray-800 rounded-xl p-6 border border-gray-700",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-gray-400 text-sm",children:"Active Categories"}),s.jsx("p",{className:"text-2xl font-bold mt-1",children:e.filter(v=>v.active).length})]}),s.jsx("div",{className:"p-3 rounded-lg bg-green-900 text-green-300",children:s.jsx(gn,{className:"w-6 h-6"})})]})}),s.jsx("div",{className:"bg-gray-800 rounded-xl p-6 border border-gray-700",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-gray-400 text-sm",children:"Inactive Categories"}),s.jsx("p",{className:"text-2xl font-bold mt-1",children:e.filter(v=>!v.active).length})]}),s.jsx("div",{className:"p-3 rounded-lg bg-gray-600 text-gray-300",children:s.jsx(gn,{className:"w-6 h-6"})})]})})]}),s.jsxs("div",{className:"relative",children:[s.jsx(Ls,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),s.jsx("input",{type:"text",placeholder:"Search categories...",value:u,onChange:v=>f(v.target.value),className:"w-full pl-10 pr-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500"})]}),a&&s.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4",children:s.jsxs("div",{className:"bg-gray-800 rounded-xl max-w-md w-full",children:[s.jsx("div",{className:"p-6 border-b border-gray-700",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx("h3",{className:"text-xl font-bold",children:o?"Edit Category":"Add New Category"}),s.jsx("button",{onClick:w,className:"text-gray-400 hover:text-white",children:s.jsx(Wt,{className:"w-6 h-6"})})]})}),s.jsxs("form",{onSubmit:y,className:"p-6 space-y-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Category Name *"}),s.jsx("input",{type:"text",name:"name",value:m.name,onChange:j,placeholder:"e.g., Electronics, Sports, Books",className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500",required:!0})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Description"}),s.jsx("textarea",{name:"description",value:m.description,onChange:j,rows:"3",placeholder:"Brief description of this category...",className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500"})]}),s.jsxs("div",{className:"flex justify-end space-x-3 pt-4",children:[s.jsx("button",{type:"button",onClick:w,className:"px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors",children:"Cancel"}),s.jsxs("button",{type:"submit",className:"px-4 py-2 bg-primary-500 hover:bg-primary-600 text-white rounded-lg flex items-center space-x-2 transition-colors",children:[s.jsx(yn,{className:"w-4 h-4"}),s.jsxs("span",{children:[o?"Update":"Add"," Category"]})]})]})]})]})}),s.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:g.map(v=>s.jsxs("div",{className:"bg-gray-800 rounded-xl border border-gray-700 p-6",children:[s.jsxs("div",{className:"flex items-start justify-between mb-4",children:[s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx("div",{className:"p-2 rounded-lg bg-primary-900 text-primary-300",children:s.jsx(Ol,{className:"w-5 h-5"})}),s.jsxs("div",{children:[s.jsx("h3",{className:"text-lg font-semibold text-white",children:v.name}),s.jsxs("p",{className:"text-sm text-gray-400",children:["/",v.slug]})]})]}),s.jsx("div",{className:"flex items-center space-x-2",children:s.jsx("button",{onClick:()=>x(v.id),className:`p-1 rounded transition-colors ${v.active?"text-green-400 hover:text-green-300":"text-gray-500 hover:text-gray-400"}`,title:v.active?"Active":"Inactive",children:v.active?s.jsx(f0,{className:"w-5 h-5"}):s.jsx(m0,{className:"w-5 h-5"})})})]}),s.jsx("p",{className:"text-gray-300 text-sm mb-4 line-clamp-2",children:v.description||"No description provided"}),s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx("span",{className:`inline-flex items-center px-2 py-1 text-xs rounded-full ${v.active?"bg-green-900 text-green-300":"bg-gray-600 text-gray-300"}`,children:v.active?"Active":"Inactive"}),s.jsxs("div",{className:"flex space-x-2",children:[s.jsx("button",{onClick:()=>p(v),className:"text-yellow-400 hover:text-yellow-300 p-1",title:"Edit Category",children:s.jsx(tl,{className:"w-4 h-4"})}),s.jsx("button",{onClick:()=>d(v.id),className:"text-red-400 hover:text-red-300 p-1",title:"Delete Category",children:s.jsx(nl,{className:"w-4 h-4"})})]})]})]},v.id))}),g.length===0&&s.jsxs("div",{className:"text-center py-12",children:[s.jsx(Ol,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),s.jsx("h3",{className:"text-lg font-medium text-gray-300 mb-2",children:"No categories found"}),s.jsx("p",{className:"text-gray-400 mb-4",children:u?"Try adjusting your search terms":"Get started by adding your first category"}),!u&&s.jsx("button",{onClick:()=>i(!0),className:"btn-primary",children:"Add Category"})]})]})},_0=()=>{var m,h,g,j,y,w,p,d,x,v,k,C,E,T,F,M,_,Ve,We,Rt,_n,Gt,Kt,b,L,z,V;const{products:e}=Zs(),{customers:t,getCustomerStats:r}=Od(),[n,l]=N.useState("30d"),[a,i]=N.useState({});N.useEffect(()=>{o()},[e,t,n]);const o=()=>{const O=r(),G=e.length,H=e.filter(ye=>ye.featured).length,xe=e.reduce((ye,it)=>ye+it.price,0)/G||0,ge=e.reduce((ye,it)=>(ye[it.category]=(ye[it.category]||0)+1,ye),{}),Ke=c(n),Wd=u(n),Hd=e.map(ye=>({...ye,sales:Math.floor(Math.random()*100)+10,revenue:(Math.floor(Math.random()*100)+10)*ye.price})).sort((ye,it)=>it.revenue-ye.revenue).slice(0,5),zi=Math.floor(t.length*.3),Qd=t.length-zi,Yd=15.3;i({revenue:{total:O.totalRevenue,growth:23.5,data:Ke},orders:{total:t.reduce((ye,it)=>ye+it.totalOrders,0),growth:18.2,data:Wd},customers:{total:O.total,new:zi,returning:Qd,growth:Yd,vip:O.vip},products:{total:G,featured:H,avgPrice:xe,categories:ge,topPerforming:Hd},conversion:{rate:3.2,visitors:12450,sales:398}})},c=O=>{const G=O==="7d"?7:O==="30d"?30:90;return Array.from({length:G},(H,xe)=>({date:new Date(Date.now()-(G-xe-1)*24*60*60*1e3).toLocaleDateString(),value:Math.floor(Math.random()*1e3)+200}))},u=O=>{const G=O==="7d"?7:O==="30d"?30:90;return Array.from({length:G},(H,xe)=>({date:new Date(Date.now()-(G-xe-1)*24*60*60*1e3).toLocaleDateString(),value:Math.floor(Math.random()*50)+5}))},f=({title:O,value:G,growth:H,icon:xe,color:ge="blue"})=>s.jsx("div",{className:"bg-gray-800 rounded-xl p-6 border border-gray-700",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-gray-400 text-sm",children:O}),s.jsx("p",{className:"text-2xl font-bold mt-1",children:G}),H!==void 0&&s.jsxs("div",{className:"flex items-center mt-2",children:[H>=0?s.jsx(Vd,{className:"w-4 h-4 text-green-400 mr-1"}):s.jsx(p0,{className:"w-4 h-4 text-red-400 mr-1"}),s.jsxs("span",{className:`text-sm ${H>=0?"text-green-400":"text-red-400"}`,children:[Math.abs(H),"%"]}),s.jsx("span",{className:"text-gray-400 text-sm ml-1",children:"vs last month"})]})]}),s.jsx("div",{className:`p-3 rounded-lg bg-${ge}-900 text-${ge}-300`,children:s.jsx(xe,{className:"w-6 h-6"})})]})});return s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("h2",{className:"text-2xl font-bold",children:"Analytics Dashboard"}),s.jsx("p",{className:"text-gray-400 mt-1",children:"Track your business performance"})]}),s.jsx("div",{className:"flex bg-gray-700 rounded-lg p-1",children:["7d","30d","90d"].map(O=>s.jsx("button",{onClick:()=>l(O),className:`px-4 py-2 rounded-md text-sm font-medium transition-colors ${n===O?"bg-primary-600 text-white":"text-gray-300 hover:text-white"}`,children:O==="7d"?"7 Days":O==="30d"?"30 Days":"90 Days"},O))})]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[s.jsx(f,{title:"Total Revenue",value:`$${((h=(m=a.revenue)==null?void 0:m.total)==null?void 0:h.toFixed(2))||"0.00"}`,growth:(g=a.revenue)==null?void 0:g.growth,icon:Rs,color:"green"}),s.jsx(f,{title:"Total Orders",value:((j=a.orders)==null?void 0:j.total)||0,growth:(y=a.orders)==null?void 0:y.growth,icon:kr,color:"blue"}),s.jsx(f,{title:"Total Customers",value:((w=a.customers)==null?void 0:w.total)||0,growth:(p=a.customers)==null?void 0:p.growth,icon:zs,color:"purple"}),s.jsx(f,{title:"Products",value:((d=a.products)==null?void 0:d.total)||0,icon:gn,color:"orange"})]}),s.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[s.jsxs("div",{className:"bg-gray-800 rounded-xl p-6 border border-gray-700",children:[s.jsxs("div",{className:"flex items-center justify-between mb-6",children:[s.jsx("h3",{className:"text-lg font-semibold",children:"Revenue Trend"}),s.jsx(Ad,{className:"w-5 h-5 text-gray-400"})]}),s.jsx("div",{className:"h-64 flex items-end justify-between space-x-1",children:(v=(x=a.revenue)==null?void 0:x.data)==null?void 0:v.slice(-7).map((O,G)=>s.jsxs("div",{className:"flex flex-col items-center flex-1",children:[s.jsx("div",{className:"bg-primary-500 rounded-t w-full",style:{height:`${O.value/Math.max(...a.revenue.data.map(H=>H.value))*200}px`}}),s.jsx("span",{className:"text-xs text-gray-400 mt-2 transform -rotate-45",children:O.date.split("/").slice(0,2).join("/")})]},G))})]}),s.jsxs("div",{className:"bg-gray-800 rounded-xl p-6 border border-gray-700",children:[s.jsxs("div",{className:"flex items-center justify-between mb-6",children:[s.jsx("h3",{className:"text-lg font-semibold",children:"Product Categories"}),s.jsx(c0,{className:"w-5 h-5 text-gray-400"})]}),s.jsx("div",{className:"space-y-4",children:Object.entries(((k=a.products)==null?void 0:k.categories)||{}).map(([O,G],H)=>{var Ke;const xe=["bg-blue-500","bg-green-500","bg-purple-500","bg-orange-500"],ge=(G/((Ke=a.products)==null?void 0:Ke.total)*100).toFixed(1);return s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center",children:[s.jsx("div",{className:`w-3 h-3 rounded-full ${xe[H%xe.length]} mr-3`}),s.jsx("span",{className:"text-gray-300",children:O})]}),s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("span",{className:"text-gray-400",children:G}),s.jsxs("span",{className:"text-sm text-gray-500",children:["(",ge,"%)"]})]})]},O)})})]})]}),s.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[s.jsxs("div",{className:"bg-gray-800 rounded-xl p-6 border border-gray-700",children:[s.jsxs("div",{className:"flex items-center justify-between mb-6",children:[s.jsx("h3",{className:"text-lg font-semibold",children:"Customer Insights"}),s.jsx(zs,{className:"w-5 h-5 text-gray-400"})]}),s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-gray-400",children:"New Customers"}),s.jsx("span",{className:"text-green-400",children:((C=a.customers)==null?void 0:C.new)||0})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-gray-400",children:"Returning Customers"}),s.jsx("span",{className:"text-blue-400",children:((E=a.customers)==null?void 0:E.returning)||0})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-gray-400",children:"VIP Customers"}),s.jsx("span",{className:"text-purple-400",children:((T=a.customers)==null?void 0:T.vip)||0})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-gray-400",children:"Avg Order Value"}),s.jsxs("span",{className:"text-orange-400",children:["$",(((F=a.revenue)==null?void 0:F.total)/((M=a.orders)==null?void 0:M.total)||0).toFixed(2)]})]})]})]}),s.jsxs("div",{className:"bg-gray-800 rounded-xl p-6 border border-gray-700",children:[s.jsxs("div",{className:"flex items-center justify-between mb-6",children:[s.jsx("h3",{className:"text-lg font-semibold",children:"Conversion"}),s.jsx(d0,{className:"w-5 h-5 text-gray-400"})]}),s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"text-center",children:[s.jsxs("div",{className:"text-3xl font-bold text-primary-400",children:[((_=a.conversion)==null?void 0:_.rate)||0,"%"]}),s.jsx("div",{className:"text-gray-400 text-sm",children:"Conversion Rate"})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-gray-400",children:"Visitors"}),s.jsx("span",{className:"text-blue-400",children:((We=(Ve=a.conversion)==null?void 0:Ve.visitors)==null?void 0:We.toLocaleString())||0})]}),s.jsxs("div",{className:"flex justify-between",children:[s.jsx("span",{className:"text-gray-400",children:"Sales"}),s.jsx("span",{className:"text-green-400",children:((Rt=a.conversion)==null?void 0:Rt.sales)||0})]})]})]}),s.jsxs("div",{className:"bg-gray-800 rounded-xl p-6 border border-gray-700",children:[s.jsxs("div",{className:"flex items-center justify-between mb-6",children:[s.jsx("h3",{className:"text-lg font-semibold",children:"Top Products"}),s.jsx(Gh,{className:"w-5 h-5 text-gray-400"})]}),s.jsx("div",{className:"space-y-3",children:(Gt=(_n=a.products)==null?void 0:_n.topPerforming)==null?void 0:Gt.slice(0,5).map((O,G)=>{var H;return s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{className:"flex items-center",children:[s.jsx("div",{className:"w-8 h-8 bg-primary-500 rounded-full flex items-center justify-center text-xs font-bold mr-3",children:G+1}),s.jsxs("div",{children:[s.jsx("div",{className:"text-sm font-medium text-white truncate max-w-32",children:O.name}),s.jsxs("div",{className:"text-xs text-gray-400",children:[O.sales," sales"]})]})]}),s.jsxs("div",{className:"text-sm font-medium text-green-400",children:["$",(H=O.revenue)==null?void 0:H.toFixed(0)]})]},O.id)})})]})]}),s.jsxs("div",{className:"bg-gray-800 rounded-xl p-6 border border-gray-700",children:[s.jsxs("div",{className:"flex items-center justify-between mb-6",children:[s.jsx("h3",{className:"text-lg font-semibold",children:"Performance Summary"}),s.jsx(Yh,{className:"w-5 h-5 text-gray-400"})]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[s.jsxs("div",{className:"text-center",children:[s.jsxs("div",{className:"text-2xl font-bold text-green-400 mb-2",children:["$",((b=(Kt=a.products)==null?void 0:Kt.avgPrice)==null?void 0:b.toFixed(2))||"0.00"]}),s.jsx("div",{className:"text-gray-400 text-sm",children:"Average Product Price"})]}),s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"text-2xl font-bold text-blue-400 mb-2",children:((L=a.products)==null?void 0:L.featured)||0}),s.jsx("div",{className:"text-gray-400 text-sm",children:"Featured Products"})]}),s.jsxs("div",{className:"text-center",children:[s.jsxs("div",{className:"text-2xl font-bold text-purple-400 mb-2",children:[(((z=a.customers)==null?void 0:z.vip)/((V=a.customers)==null?void 0:V.total)*100||0).toFixed(1),"%"]}),s.jsx("div",{className:"text-gray-400 text-sm",children:"VIP Customer Rate"})]})]})]})]})},T0=()=>{const{user:e,updateProfile:t}=el(),[r,n]=N.useState("profile"),[l,a]=N.useState({appName:"YourShop",appDescription:"Your destination for premium fashion and lifestyle products",appLogo:"YS",businessName:"YourShop",email:"<EMAIL>",phone:"+****************",address:"Your Address Here, Your City, State 12345",website:"https://yourshop.com",primaryColor:"#ec4899",secondaryColor:"#6b7280",emailNotifications:!0,orderNotifications:!0,marketingEmails:!1,twoFactorAuth:!1,sessionTimeout:30}),[i,o]=N.useState({name:(e==null?void 0:e.name)||"",email:(e==null?void 0:e.email)||"",username:(e==null?void 0:e.username)||"",avatar:(e==null?void 0:e.avatar)||"",phone:(e==null?void 0:e.phone)||"",address:(e==null?void 0:e.address)||""});N.useEffect(()=>{const g=localStorage.getItem("ecommerce_settings");g&&a(j=>({...j,...JSON.parse(g)}))},[]);const c=(g,j)=>{a(y=>{const w={...y,[g]:j};return localStorage.setItem("ecommerce_settings",JSON.stringify(w)),w})},u=(g,j)=>{o(y=>({...y,[g]:j}))},f=()=>{t(i),alert("Profile updated successfully!")},m=()=>{localStorage.setItem("ecommerce_settings",JSON.stringify(l)),alert("Settings saved successfully! Refresh the page to see changes.")},h=[{id:"profile",name:"Profile",icon:Ms},{id:"app",name:"App Settings",icon:u0},{id:"theme",name:"Theme",icon:o0},{id:"notifications",name:"Notifications",icon:Kh},{id:"security",name:"Security",icon:rl}];return s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{children:[s.jsx("h2",{className:"text-2xl font-bold",children:"Settings"}),s.jsx("p",{className:"text-gray-400 mt-1",children:"Manage your account and application settings"})]}),s.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-6",children:[s.jsx("div",{className:"lg:col-span-1",children:s.jsx("div",{className:"bg-gray-800 rounded-xl border border-gray-700",children:s.jsx("nav",{className:"p-4 space-y-2",children:h.map(g=>s.jsxs("button",{onClick:()=>n(g.id),className:`w-full flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors ${r===g.id?"bg-primary-600 text-white":"text-gray-300 hover:bg-gray-700 hover:text-white"}`,children:[s.jsx(g.icon,{className:"w-5 h-5 mr-3"}),g.name]},g.id))})})}),s.jsx("div",{className:"lg:col-span-3",children:s.jsxs("div",{className:"bg-gray-800 rounded-xl border border-gray-700 p-6",children:[r==="profile"&&s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx("h3",{className:"text-xl font-semibold",children:"Profile Settings"}),s.jsxs("button",{onClick:f,className:"btn-primary flex items-center space-x-2",children:[s.jsx(yn,{className:"w-4 h-4"}),s.jsx("span",{children:"Save Profile"})]})]}),s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsxs("div",{className:"relative",children:[s.jsx("img",{src:i.avatar||`https://ui-avatars.com/api/?name=${i.name}&background=6366f1&color=fff`,alt:"Profile",className:"w-20 h-20 rounded-full object-cover"}),s.jsx("button",{className:"absolute bottom-0 right-0 p-1 bg-primary-500 rounded-full text-white hover:bg-primary-600",children:s.jsx(qh,{className:"w-4 h-4"})})]}),s.jsxs("div",{children:[s.jsx("h4",{className:"text-lg font-medium",children:i.name}),s.jsx("p",{className:"text-gray-400",children:(e==null?void 0:e.role)==="admin"?"Administrator":"Customer"})]})]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Full Name"}),s.jsx("input",{type:"text",value:i.name,onChange:g=>u("name",g.target.value),className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500"})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Username"}),s.jsx("input",{type:"text",value:i.username,onChange:g=>u("username",g.target.value),className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500"})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Email"}),s.jsx("input",{type:"email",value:i.email,onChange:g=>u("email",g.target.value),className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500"})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Avatar URL"}),s.jsx("input",{type:"url",value:i.avatar,onChange:g=>u("avatar",g.target.value),placeholder:"https://example.com/avatar.jpg",className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500"})]})]})]}),r==="app"&&s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx("h3",{className:"text-xl font-semibold",children:"Application Settings"}),s.jsxs("button",{onClick:m,className:"btn-primary flex items-center space-x-2",children:[s.jsx(yn,{className:"w-4 h-4"}),s.jsx("span",{children:"Save Settings"})]})]}),s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"App Name"}),s.jsx("input",{type:"text",value:l.appName,onChange:g=>c("appName",g.target.value),className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500"})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"App Description"}),s.jsx("textarea",{value:l.appDescription,onChange:g=>c("appDescription",g.target.value),rows:"3",className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500"})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Logo Text"}),s.jsx("input",{type:"text",value:l.appLogo,onChange:g=>c("appLogo",g.target.value),maxLength:"3",className:"w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500"})]}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Business Email"}),s.jsxs("div",{className:"relative",children:[s.jsx(xn,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),s.jsx("input",{type:"email",value:l.email,onChange:g=>c("email",g.target.value),className:"w-full pl-10 pr-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500"})]})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Phone Number"}),s.jsxs("div",{className:"relative",children:[s.jsx(Os,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),s.jsx("input",{type:"tel",value:l.phone,onChange:g=>c("phone",g.target.value),className:"w-full pl-10 pr-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500"})]})]})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Business Address"}),s.jsxs("div",{className:"relative",children:[s.jsx(Li,{className:"absolute left-3 top-3 text-gray-400 w-4 h-4"}),s.jsx("textarea",{value:l.address,onChange:g=>c("address",g.target.value),rows:"2",className:"w-full pl-10 pr-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500"})]})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Website URL"}),s.jsxs("div",{className:"relative",children:[s.jsx(n0,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),s.jsx("input",{type:"url",value:l.website,onChange:g=>c("website",g.target.value),className:"w-full pl-10 pr-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500"})]})]})]})]}),r==="theme"&&s.jsxs("div",{className:"space-y-6",children:[s.jsx("h3",{className:"text-xl font-semibold",children:"Theme Settings"}),s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Primary Color"}),s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx("input",{type:"color",value:l.primaryColor,onChange:g=>c("primaryColor",g.target.value),className:"w-12 h-10 rounded border border-gray-600"}),s.jsx("input",{type:"text",value:l.primaryColor,onChange:g=>c("primaryColor",g.target.value),className:"flex-1 px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500"})]})]}),s.jsxs("div",{children:[s.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Secondary Color"}),s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx("input",{type:"color",value:l.secondaryColor,onChange:g=>c("secondaryColor",g.target.value),className:"w-12 h-10 rounded border border-gray-600"}),s.jsx("input",{type:"text",value:l.secondaryColor,onChange:g=>c("secondaryColor",g.target.value),className:"flex-1 px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500"})]})]})]}),s.jsxs("div",{className:"p-4 bg-gray-700 rounded-lg",children:[s.jsx("h4",{className:"font-medium mb-2",children:"Preview"}),s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsx("button",{style:{backgroundColor:l.primaryColor},className:"px-4 py-2 text-white rounded-lg",children:"Primary Button"}),s.jsx("button",{style:{backgroundColor:l.secondaryColor},className:"px-4 py-2 text-white rounded-lg",children:"Secondary Button"})]})]})]}),r==="notifications"&&s.jsxs("div",{className:"space-y-6",children:[s.jsx("h3",{className:"text-xl font-semibold",children:"Notification Settings"}),s.jsx("div",{className:"space-y-4",children:[{key:"emailNotifications",label:"Email Notifications",desc:"Receive notifications via email"},{key:"orderNotifications",label:"Order Notifications",desc:"Get notified about new orders"},{key:"marketingEmails",label:"Marketing Emails",desc:"Receive promotional emails"}].map(g=>s.jsxs("div",{className:"flex items-center justify-between p-4 bg-gray-700 rounded-lg",children:[s.jsxs("div",{children:[s.jsx("h4",{className:"font-medium",children:g.label}),s.jsx("p",{className:"text-sm text-gray-400",children:g.desc})]}),s.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[s.jsx("input",{type:"checkbox",checked:l[g.key],onChange:j=>c(g.key,j.target.checked),className:"sr-only peer"}),s.jsx("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"})]})]},g.key))})]}),r==="security"&&s.jsxs("div",{className:"space-y-6",children:[s.jsx("h3",{className:"text-xl font-semibold",children:"Security Settings"}),s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{className:"flex items-center justify-between p-4 bg-gray-700 rounded-lg",children:[s.jsxs("div",{children:[s.jsx("h4",{className:"font-medium",children:"Two-Factor Authentication"}),s.jsx("p",{className:"text-sm text-gray-400",children:"Add an extra layer of security"})]}),s.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[s.jsx("input",{type:"checkbox",checked:l.twoFactorAuth,onChange:g=>c("twoFactorAuth",g.target.checked),className:"sr-only peer"}),s.jsx("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"})]})]}),s.jsxs("div",{className:"p-4 bg-gray-700 rounded-lg",children:[s.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-1",children:"Session Timeout (minutes)"}),s.jsx("input",{type:"number",value:l.sessionTimeout,onChange:g=>c("sessionTimeout",parseInt(g.target.value)),min:"5",max:"120",className:"w-full px-3 py-2 bg-gray-600 border border-gray-500 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-primary-500"})]}),s.jsxs("div",{className:"p-4 bg-yellow-900 border border-yellow-700 rounded-lg",children:[s.jsx("h4",{className:"font-medium text-yellow-300 mb-2",children:"Change Password"}),s.jsx("p",{className:"text-sm text-yellow-200 mb-3",children:"For security reasons, password changes require re-authentication."}),s.jsx("button",{className:"btn-secondary",children:"Change Password"})]})]})]})]})})]})]})},R0=()=>{const[e,t]=N.useState(!1),[r,n]=N.useState("dashboard"),{user:l,logout:a}=el(),i=[{name:"Total Revenue",value:"$45,231",change:"+20.1%",icon:Rs,color:"text-green-600"},{name:"Orders",value:"1,234",change:"+15.3%",icon:kr,color:"text-blue-600"},{name:"Customers",value:"2,345",change:"+8.2%",icon:zs,color:"text-purple-600"},{name:"Products",value:"567",change:"+3.1%",icon:gn,color:"text-orange-600"}],o=[{id:"#1234",customer:"John Doe",amount:"$129.99",status:"Completed",date:"2024-01-15"},{id:"#1235",customer:"Jane Smith",amount:"$89.50",status:"Processing",date:"2024-01-15"},{id:"#1236",customer:"Mike Johnson",amount:"$199.99",status:"Shipped",date:"2024-01-14"},{id:"#1237",customer:"Sarah Wilson",amount:"$79.99",status:"Pending",date:"2024-01-14"}],c=[{name:"Premium Cotton T-Shirt",sales:234,revenue:"$7,020"},{name:"Elegant Summer Dress",sales:189,revenue:"$15,111"},{name:"Leather Crossbody Bag",sales:156,revenue:"$23,400"},{name:"Classic Denim Jacket",sales:143,revenue:"$12,870"}],u=[{name:"Dashboard",icon:Ad,key:"dashboard"},{name:"Orders",icon:kr,key:"orders"},{name:"Products",icon:gn,key:"products"},{name:"Categories",icon:Tag,key:"categories"},{name:"Customers",icon:zs,key:"customers"},{name:"Analytics",icon:Vd,key:"analytics"},{name:"Settings",icon:Ud,key:"settings"}];return s.jsxs("div",{className:"min-h-screen bg-gray-900 text-white",children:[s.jsxs("div",{className:`fixed inset-y-0 left-0 z-50 w-64 bg-gray-800 transform ${e?"translate-x-0":"-translate-x-full"} transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0`,children:[s.jsxs("div",{className:"flex items-center justify-between h-16 px-6 border-b border-gray-700",children:[s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx("div",{className:"w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-lg flex items-center justify-center",children:s.jsx("span",{className:"text-white font-bold text-lg",children:"YS"})}),s.jsx("span",{className:"text-xl font-bold",children:"YourShop Admin"})]}),s.jsx("button",{onClick:()=>t(!1),className:"lg:hidden text-gray-400 hover:text-white",children:s.jsx(Wt,{className:"w-6 h-6"})})]}),s.jsxs("nav",{className:"mt-6",children:[s.jsx("div",{className:"px-6 space-y-1",children:u.map(f=>s.jsxs("button",{onClick:()=>n(f.key),className:`flex items-center w-full px-4 py-3 text-sm font-medium rounded-lg transition-colors ${r===f.key?"bg-primary-600 text-white":"text-gray-300 hover:bg-gray-700 hover:text-white"}`,children:[s.jsx(f.icon,{className:"w-5 h-5 mr-3"}),f.name]},f.name))}),s.jsx("div",{className:"mt-8 pt-6 border-t border-gray-700",children:s.jsx("div",{className:"px-6",children:s.jsxs("button",{className:"flex items-center w-full px-4 py-3 text-sm font-medium text-gray-300 rounded-lg hover:bg-gray-700 hover:text-white transition-colors",children:[s.jsx(Id,{className:"w-5 h-5 mr-3"}),"Sign Out"]})})})]})]}),s.jsxs("div",{className:"lg:pl-64",children:[s.jsx("header",{className:"bg-gray-800 border-b border-gray-700",children:s.jsxs("div",{className:"flex items-center justify-between h-16 px-6",children:[s.jsxs("div",{className:"flex items-center",children:[s.jsx("button",{onClick:()=>t(!0),className:"lg:hidden text-gray-400 hover:text-white mr-4",children:s.jsx(Dd,{className:"w-6 h-6"})}),s.jsx("h1",{className:"text-2xl font-bold",children:r.charAt(0).toUpperCase()+r.slice(1)})]}),s.jsxs("div",{className:"flex items-center space-x-4",children:[s.jsxs("div",{className:"text-right",children:[s.jsx("p",{className:"text-sm text-gray-300",children:"Welcome back,"}),s.jsx("p",{className:"font-medium",children:(l==null?void 0:l.name)||"Admin User"})]}),s.jsx("img",{src:(l==null?void 0:l.avatar)||`https://ui-avatars.com/api/?name=${(l==null?void 0:l.name)||"Admin User"}&background=6366f1&color=fff`,alt:(l==null?void 0:l.name)||"Admin",className:"w-10 h-10 rounded-full object-cover"})]})]})}),s.jsxs("main",{className:"p-6",children:[r==="dashboard"&&s.jsxs(s.Fragment,{children:[s.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:i.map(f=>s.jsx("div",{className:"bg-gray-800 rounded-xl p-6 border border-gray-700",children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx("p",{className:"text-gray-400 text-sm",children:f.name}),s.jsx("p",{className:"text-2xl font-bold mt-1",children:f.value}),s.jsxs("p",{className:`text-sm mt-1 ${f.color}`,children:[f.change," from last month"]})]}),s.jsx("div",{className:`p-3 rounded-lg bg-gray-700 ${f.color}`,children:s.jsx(f.icon,{className:"w-6 h-6"})})]})},f.name))}),s.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[s.jsxs("div",{className:"bg-gray-800 rounded-xl border border-gray-700",children:[s.jsx("div",{className:"p-6 border-b border-gray-700",children:s.jsx("h2",{className:"text-xl font-bold",children:"Recent Orders"})}),s.jsx("div",{className:"p-6",children:s.jsx("div",{className:"space-y-4",children:o.map(f=>s.jsxs("div",{className:"flex items-center justify-between p-4 bg-gray-700 rounded-lg",children:[s.jsxs("div",{children:[s.jsx("p",{className:"font-medium",children:f.id}),s.jsx("p",{className:"text-sm text-gray-400",children:f.customer}),s.jsx("p",{className:"text-xs text-gray-500",children:f.date})]}),s.jsxs("div",{className:"text-right",children:[s.jsx("p",{className:"font-medium",children:f.amount}),s.jsx("span",{className:`inline-block px-2 py-1 text-xs rounded-full ${f.status==="Completed"?"bg-green-900 text-green-300":f.status==="Processing"?"bg-yellow-900 text-yellow-300":f.status==="Shipped"?"bg-blue-900 text-blue-300":"bg-gray-600 text-gray-300"}`,children:f.status})]}),s.jsxs("div",{className:"flex space-x-2",children:[s.jsx("button",{className:"p-1 text-gray-400 hover:text-white",children:s.jsx(Ri,{className:"w-4 h-4"})}),s.jsx("button",{className:"p-1 text-gray-400 hover:text-white",children:s.jsx(tl,{className:"w-4 h-4"})})]})]},f.id))})})]}),s.jsxs("div",{className:"bg-gray-800 rounded-xl border border-gray-700",children:[s.jsx("div",{className:"p-6 border-b border-gray-700",children:s.jsx("h2",{className:"text-xl font-bold",children:"Top Products"})}),s.jsx("div",{className:"p-6",children:s.jsx("div",{className:"space-y-4",children:c.map((f,m)=>s.jsxs("div",{className:"flex items-center justify-between p-4 bg-gray-700 rounded-lg",children:[s.jsxs("div",{className:"flex items-center space-x-3",children:[s.jsx("div",{className:"w-8 h-8 bg-primary-500 rounded-full flex items-center justify-center text-sm font-bold",children:m+1}),s.jsxs("div",{children:[s.jsx("p",{className:"font-medium",children:f.name}),s.jsxs("p",{className:"text-sm text-gray-400",children:[f.sales," sales"]})]})]}),s.jsx("div",{className:"text-right",children:s.jsx("p",{className:"font-medium text-green-400",children:f.revenue})})]},f.name))})})]})]}),s.jsxs("div",{className:"mt-8 bg-gray-800 rounded-xl border border-gray-700",children:[s.jsx("div",{className:"p-6 border-b border-gray-700",children:s.jsx("h2",{className:"text-xl font-bold",children:"System Status"})}),s.jsx("div",{className:"p-6",children:s.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"w-16 h-16 bg-green-900 rounded-full flex items-center justify-center mx-auto mb-3",children:s.jsx("div",{className:"w-8 h-8 bg-green-500 rounded-full"})}),s.jsx("p",{className:"font-medium",children:"Server Status"}),s.jsx("p",{className:"text-sm text-green-400",children:"Online"})]}),s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"w-16 h-16 bg-blue-900 rounded-full flex items-center justify-center mx-auto mb-3",children:s.jsx("div",{className:"w-8 h-8 bg-blue-500 rounded-full"})}),s.jsx("p",{className:"font-medium",children:"Database"}),s.jsx("p",{className:"text-sm text-blue-400",children:"Connected"})]}),s.jsxs("div",{className:"text-center",children:[s.jsx("div",{className:"w-16 h-16 bg-yellow-900 rounded-full flex items-center justify-center mx-auto mb-3",children:s.jsx("div",{className:"w-8 h-8 bg-yellow-500 rounded-full"})}),s.jsx("p",{className:"font-medium",children:"Payment Gateway"}),s.jsx("p",{className:"text-sm text-yellow-400",children:"Maintenance"})]})]})})]})]}),r==="products"&&s.jsx(b0,{}),r==="categories"&&s.jsx(P0,{}),r==="orders"&&s.jsxs("div",{className:"bg-gray-800 rounded-xl p-6 border border-gray-700",children:[s.jsx("h2",{className:"text-2xl font-bold mb-4",children:"Order Management"}),s.jsx("p",{className:"text-gray-400",children:"Order management functionality coming soon..."})]}),r==="customers"&&s.jsx(E0,{}),r==="analytics"&&s.jsx(_0,{}),r==="settings"&&s.jsx(T0,{})]})]}),e&&s.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden",onClick:()=>t(!1)})]})};function O0(){return s.jsx(Vh,{children:s.jsx(Bh,{children:s.jsx(Dh,{children:s.jsx(Oh,{children:s.jsx(zh,{children:s.jsx(_h,{children:s.jsx(wh,{children:s.jsxs("div",{className:"min-h-screen bg-gray-50",children:[s.jsx(j0,{}),s.jsx("main",{className:"flex-1",children:s.jsxs(ph,{children:[s.jsx(Xt,{path:"/",element:s.jsx(w0,{})}),s.jsx(Xt,{path:"/product/:id",element:s.jsx(S0,{})}),s.jsx(Xt,{path:"/cart",element:s.jsx(k0,{})}),s.jsx(Xt,{path:"/checkout",element:s.jsx(C0,{})}),s.jsx(Xt,{path:"/admin",element:s.jsx(R0,{})})]})}),s.jsx(N0,{})]})})})})})})})})}Ml.createRoot(document.getElementById("root")).render(s.jsx(xc.StrictMode,{children:s.jsx(O0,{})}));
