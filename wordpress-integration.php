<?php
/*
Plugin Name: Modern eCommerce Integration
Description: Integrates the React eCommerce app into WordPress
Version: 1.0
*/

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Add shortcode for eCommerce app
function modern_ecommerce_shortcode($atts) {
    $atts = shortcode_atts(array(
        'page' => 'home'
    ), $atts);
    
    // Get the plugin URL
    $plugin_url = plugin_dir_url(__FILE__);
    
    ob_start();
    ?>
    <div id="modern-ecommerce-root"></div>
    
    <!-- Load the built CSS and JS files -->
    <link rel="stylesheet" href="<?php echo $plugin_url; ?>dist/assets/index-9e608d25.css">
    <script type="module" src="<?php echo $plugin_url; ?>dist/assets/index-da0b6e8a.js"></script>
    
    <style>
        /* Ensure the app takes full width */
        #modern-ecommerce-root {
            width: 100%;
            min-height: 500px;
        }
        
        /* Override WordPress theme styles if needed */
        #modern-ecommerce-root * {
            box-sizing: border-box;
        }
    </style>
    
    <script>
        // Initialize the React app
        document.addEventListener('DOMContentLoaded', function() {
            // The React app will automatically mount to #root
            // But we need to change it to our custom root
            const originalRoot = document.getElementById('root');
            const newRoot = document.getElementById('modern-ecommerce-root');
            
            if (originalRoot && newRoot) {
                newRoot.innerHTML = originalRoot.innerHTML;
                originalRoot.style.display = 'none';
            }
        });
    </script>
    <?php
    
    return ob_get_clean();
}

// Register the shortcode
add_shortcode('modern_ecommerce', 'modern_ecommerce_shortcode');

// Add admin menu
function modern_ecommerce_admin_menu() {
    add_menu_page(
        'Modern eCommerce',
        'eCommerce',
        'manage_options',
        'modern-ecommerce',
        'modern_ecommerce_admin_page',
        'dashicons-cart',
        30
    );
}
add_action('admin_menu', 'modern_ecommerce_admin_menu');

// Admin page content
function modern_ecommerce_admin_page() {
    ?>
    <div class="wrap">
        <h1>Modern eCommerce Settings</h1>
        <div class="card">
            <h2>How to Use</h2>
            <p>Use the shortcode <code>[modern_ecommerce]</code> in any page or post to display the eCommerce app.</p>
            
            <h3>Examples:</h3>
            <ul>
                <li><code>[modern_ecommerce]</code> - Display the full eCommerce app</li>
                <li><code>[modern_ecommerce page="admin"]</code> - Display admin dashboard</li>
            </ul>
            
            <h3>Setup Instructions:</h3>
            <ol>
                <li>Upload the <code>dist</code> folder contents to this plugin directory</li>
                <li>Create a new page in WordPress</li>
                <li>Add the shortcode <code>[modern_ecommerce]</code> to the page content</li>
                <li>Publish the page</li>
            </ol>
        </div>
    </div>
    <?php
}
?>
