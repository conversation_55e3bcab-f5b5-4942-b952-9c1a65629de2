import React, { useState } from 'react';
import {
  BarChart3,
  Users,
  Package,
  DollarSign,
  TrendingUp,
  ShoppingCart,
  Settings,
  LogOut,
  Menu,
  X,
  Tag
} from 'lucide-react';

const SimpleAdminDashboard = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('dashboard');

  const sidebarItems = [
    { name: 'Dashboard', icon: BarChart3, key: 'dashboard' },
    { name: 'Products', icon: Package, key: 'products' },
    { name: 'Categories', icon: Tag, key: 'categories' },
    { name: 'Customers', icon: Users, key: 'customers' },
    { name: 'Analytics', icon: TrendingUp, key: 'analytics' },
    { name: 'Settings', icon: Settings, key: 'settings' },
  ];

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      {/* Sidebar */}
      <div className={`fixed inset-y-0 left-0 z-50 w-64 bg-gray-800 transform ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'} transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0`}>
        <div className="flex items-center justify-between h-16 px-6 border-b border-gray-700">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-br from-pink-500 to-pink-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-lg">YS</span>
            </div>
            <span className="text-xl font-bold">YourShop Admin</span>
          </div>
          <button
            onClick={() => setSidebarOpen(false)}
            className="lg:hidden text-gray-400 hover:text-white"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        <nav className="mt-6">
          <div className="px-6 space-y-1">
            {sidebarItems.map((item) => (
              <button
                key={item.name}
                onClick={() => setActiveTab(item.key)}
                className={`flex items-center w-full px-4 py-3 text-sm font-medium rounded-lg transition-colors ${
                  activeTab === item.key
                    ? 'bg-pink-600 text-white'
                    : 'text-gray-300 hover:bg-gray-700 hover:text-white'
                }`}
              >
                <item.icon className="w-5 h-5 mr-3" />
                {item.name}
              </button>
            ))}
          </div>
          
          <div className="mt-8 pt-6 border-t border-gray-700">
            <div className="px-6">
              <button className="flex items-center w-full px-4 py-3 text-sm font-medium text-gray-300 rounded-lg hover:bg-gray-700 hover:text-white transition-colors">
                <LogOut className="w-5 h-5 mr-3" />
                Sign Out
              </button>
            </div>
          </div>
        </nav>
      </div>

      {/* Main Content */}
      <div className="lg:pl-64">
        {/* Header */}
        <header className="bg-gray-800 border-b border-gray-700">
          <div className="flex items-center justify-between h-16 px-6">
            <div className="flex items-center">
              <button
                onClick={() => setSidebarOpen(true)}
                className="lg:hidden text-gray-400 hover:text-white mr-4"
              >
                <Menu className="w-6 h-6" />
              </button>
              <h1 className="text-2xl font-bold">
                {activeTab.charAt(0).toUpperCase() + activeTab.slice(1)}
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              <div className="text-right">
                <p className="text-sm text-gray-300">Welcome back,</p>
                <p className="font-medium">Admin User</p>
              </div>
              <div className="w-10 h-10 bg-pink-500 rounded-full flex items-center justify-center">
                <span className="text-sm font-medium">AU</span>
              </div>
            </div>
          </div>
        </header>

        {/* Dashboard Content */}
        <main className="p-6">
          {activeTab === 'dashboard' && (
            <div className="space-y-6">
              <h2 className="text-3xl font-bold">Dashboard Overview</h2>
              
              {/* Stats Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div className="bg-gray-800 rounded-xl p-6 border border-gray-700">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-400 text-sm">Total Revenue</p>
                      <p className="text-2xl font-bold mt-1">$45,231</p>
                      <p className="text-sm mt-1 text-green-400">+20.1% from last month</p>
                    </div>
                    <div className="p-3 rounded-lg bg-gray-700 text-green-400">
                      <DollarSign className="w-6 h-6" />
                    </div>
                  </div>
                </div>

                <div className="bg-gray-800 rounded-xl p-6 border border-gray-700">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-400 text-sm">Orders</p>
                      <p className="text-2xl font-bold mt-1">1,234</p>
                      <p className="text-sm mt-1 text-blue-400">+15.3% from last month</p>
                    </div>
                    <div className="p-3 rounded-lg bg-gray-700 text-blue-400">
                      <ShoppingCart className="w-6 h-6" />
                    </div>
                  </div>
                </div>

                <div className="bg-gray-800 rounded-xl p-6 border border-gray-700">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-400 text-sm">Customers</p>
                      <p className="text-2xl font-bold mt-1">2,345</p>
                      <p className="text-sm mt-1 text-purple-400">+8.2% from last month</p>
                    </div>
                    <div className="p-3 rounded-lg bg-gray-700 text-purple-400">
                      <Users className="w-6 h-6" />
                    </div>
                  </div>
                </div>

                <div className="bg-gray-800 rounded-xl p-6 border border-gray-700">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-400 text-sm">Products</p>
                      <p className="text-2xl font-bold mt-1">567</p>
                      <p className="text-sm mt-1 text-orange-400">+3.1% from last month</p>
                    </div>
                    <div className="p-3 rounded-lg bg-gray-700 text-orange-400">
                      <Package className="w-6 h-6" />
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-gray-800 rounded-xl p-6 border border-gray-700">
                <h3 className="text-xl font-bold mb-4">Welcome to YourShop Admin</h3>
                <p className="text-gray-400">
                  Your eCommerce admin dashboard is working! Use the sidebar to navigate between different sections:
                </p>
                <ul className="mt-4 space-y-2 text-gray-300">
                  <li>• <strong>Products:</strong> Manage your product catalog</li>
                  <li>• <strong>Categories:</strong> Add and manage product categories</li>
                  <li>• <strong>Customers:</strong> View and manage customer accounts</li>
                  <li>• <strong>Analytics:</strong> View business performance metrics</li>
                  <li>• <strong>Settings:</strong> Customize your app and profile</li>
                </ul>
              </div>
            </div>
          )}

          {activeTab === 'products' && (
            <div className="bg-gray-800 rounded-xl p-6 border border-gray-700">
              <h2 className="text-2xl font-bold mb-4">Product Management</h2>
              <p className="text-gray-400">Product management functionality is available. Loading...</p>
            </div>
          )}

          {activeTab === 'categories' && (
            <div className="bg-gray-800 rounded-xl p-6 border border-gray-700">
              <h2 className="text-2xl font-bold mb-4">Category Management</h2>
              <p className="text-gray-400">Category management functionality is available. Loading...</p>
            </div>
          )}

          {activeTab === 'customers' && (
            <div className="bg-gray-800 rounded-xl p-6 border border-gray-700">
              <h2 className="text-2xl font-bold mb-4">Customer Management</h2>
              <p className="text-gray-400">Customer management functionality is available. Loading...</p>
            </div>
          )}

          {activeTab === 'analytics' && (
            <div className="bg-gray-800 rounded-xl p-6 border border-gray-700">
              <h2 className="text-2xl font-bold mb-4">Analytics Dashboard</h2>
              <p className="text-gray-400">Analytics functionality is available. Loading...</p>
            </div>
          )}

          {activeTab === 'settings' && (
            <div className="bg-gray-800 rounded-xl p-6 border border-gray-700">
              <h2 className="text-2xl font-bold mb-4">Settings</h2>
              <p className="text-gray-400">Settings functionality is available. Loading...</p>
            </div>
          )}
        </main>
      </div>

      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}
    </div>
  );
};

export default SimpleAdminDashboard;
