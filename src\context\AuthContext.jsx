import React, { createContext, useContext, useReducer, useEffect } from 'react';

const AuthContext = createContext();

// Default admin credentials
const defaultAdmin = {
  id: 1,
  username: 'admin',
  email: '<EMAIL>',
  password: 'admin123', // In production, this should be hashed
  name: 'Admin User',
  role: 'admin',
  avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face',
  createdAt: '2024-01-01',
  lastLogin: null
};

const authReducer = (state, action) => {
  switch (action.type) {
    case 'SET_USER':
      return {
        ...state,
        user: action.payload,
        isAuthenticated: !!action.payload
      };

    case 'LOGIN_SUCCESS':
      const loginData = {
        ...action.payload,
        lastLogin: new Date().toISOString()
      };
      localStorage.setItem('ecommerce_user', JSON.stringify(loginData));
      if (loginData.role === 'admin') {
        const admins = JSON.parse(localStorage.getItem('ecommerce_admins') || '[]');
        const updatedAdmins = admins.map(admin => 
          admin.id === loginData.id ? loginData : admin
        );
        localStorage.setItem('ecommerce_admins', JSON.stringify(updatedAdmins));
      }
      return {
        ...state,
        user: loginData,
        isAuthenticated: true,
        error: null
      };

    case 'LOGIN_ERROR':
      return {
        ...state,
        user: null,
        isAuthenticated: false,
        error: action.payload
      };

    case 'LOGOUT':
      localStorage.removeItem('ecommerce_user');
      return {
        ...state,
        user: null,
        isAuthenticated: false,
        error: null
      };

    case 'REGISTER_SUCCESS':
      return {
        ...state,
        error: null
      };

    case 'REGISTER_ERROR':
      return {
        ...state,
        error: action.payload
      };

    case 'UPDATE_PROFILE':
      const updatedUser = { ...state.user, ...action.payload };
      localStorage.setItem('ecommerce_user', JSON.stringify(updatedUser));
      
      if (updatedUser.role === 'admin') {
        const admins = JSON.parse(localStorage.getItem('ecommerce_admins') || '[]');
        const updatedAdmins = admins.map(admin => 
          admin.id === updatedUser.id ? updatedUser : admin
        );
        localStorage.setItem('ecommerce_admins', JSON.stringify(updatedAdmins));
      }
      
      return {
        ...state,
        user: updatedUser
      };

    case 'CLEAR_ERROR':
      return {
        ...state,
        error: null
      };

    default:
      return state;
  }
};

const initialState = {
  user: null,
  isAuthenticated: false,
  error: null,
  loading: false
};

export const AuthProvider = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  useEffect(() => {
    // Initialize default admin if not exists
    const admins = JSON.parse(localStorage.getItem('ecommerce_admins') || '[]');
    if (admins.length === 0) {
      localStorage.setItem('ecommerce_admins', JSON.stringify([defaultAdmin]));
    }

    // Check for existing user session
    const savedUser = localStorage.getItem('ecommerce_user');
    if (savedUser) {
      try {
        const user = JSON.parse(savedUser);
        dispatch({ type: 'SET_USER', payload: user });
      } catch (error) {
        localStorage.removeItem('ecommerce_user');
      }
    }
  }, []);

  const login = async (credentials) => {
    try {
      const { email, password, isAdmin } = credentials;
      
      if (isAdmin) {
        // Admin login
        const admins = JSON.parse(localStorage.getItem('ecommerce_admins') || '[]');
        const admin = admins.find(a => 
          (a.email === email || a.username === email) && a.password === password
        );
        
        if (admin) {
          dispatch({ type: 'LOGIN_SUCCESS', payload: admin });
          return { success: true };
        } else {
          dispatch({ type: 'LOGIN_ERROR', payload: 'Invalid admin credentials' });
          return { success: false, error: 'Invalid admin credentials' };
        }
      } else {
        // Customer login
        const customers = JSON.parse(localStorage.getItem('ecommerce_customers') || '[]');
        const customer = customers.find(c => c.email === email);
        
        if (customer) {
          const customerUser = {
            ...customer,
            role: 'customer',
            password: undefined // Don't store password in session
          };
          dispatch({ type: 'LOGIN_SUCCESS', payload: customerUser });
          return { success: true };
        } else {
          dispatch({ type: 'LOGIN_ERROR', payload: 'Customer not found' });
          return { success: false, error: 'Customer not found' };
        }
      }
    } catch (error) {
      dispatch({ type: 'LOGIN_ERROR', payload: 'Login failed' });
      return { success: false, error: 'Login failed' };
    }
  };

  const register = async (userData) => {
    try {
      const { email, password, firstName, lastName, isAdmin } = userData;
      
      if (isAdmin) {
        // Admin registration
        const admins = JSON.parse(localStorage.getItem('ecommerce_admins') || '[]');
        const existingAdmin = admins.find(a => a.email === email || a.username === email);
        
        if (existingAdmin) {
          dispatch({ type: 'REGISTER_ERROR', payload: 'Admin already exists' });
          return { success: false, error: 'Admin already exists' };
        }
        
        const newAdmin = {
          id: Math.max(...admins.map(a => a.id), 0) + 1,
          username: email.split('@')[0],
          email,
          password, // In production, hash this
          name: `${firstName} ${lastName}`,
          role: 'admin',
          avatar: `https://ui-avatars.com/api/?name=${firstName}+${lastName}&background=6366f1&color=fff`,
          createdAt: new Date().toISOString(),
          lastLogin: null
        };
        
        admins.push(newAdmin);
        localStorage.setItem('ecommerce_admins', JSON.stringify(admins));
        dispatch({ type: 'REGISTER_SUCCESS' });
        return { success: true };
      } else {
        // Customer registration
        const customers = JSON.parse(localStorage.getItem('ecommerce_customers') || '[]');
        const existingCustomer = customers.find(c => c.email === email);
        
        if (existingCustomer) {
          dispatch({ type: 'REGISTER_ERROR', payload: 'Customer already exists' });
          return { success: false, error: 'Customer already exists' };
        }
        
        const newCustomer = {
          id: Math.max(...customers.map(c => c.id), 0) + 1,
          firstName,
          lastName,
          email,
          phone: userData.phone || '',
          address: userData.address || '',
          dateJoined: new Date().toISOString().split('T')[0],
          totalOrders: 0,
          totalSpent: 0,
          status: 'Active',
          lastOrder: null,
          avatar: `https://ui-avatars.com/api/?name=${firstName}+${lastName}&background=6366f1&color=fff`
        };
        
        customers.push(newCustomer);
        localStorage.setItem('ecommerce_customers', JSON.stringify(customers));
        dispatch({ type: 'REGISTER_SUCCESS' });
        return { success: true };
      }
    } catch (error) {
      dispatch({ type: 'REGISTER_ERROR', payload: 'Registration failed' });
      return { success: false, error: 'Registration failed' };
    }
  };

  const logout = () => {
    dispatch({ type: 'LOGOUT' });
  };

  const updateProfile = (profileData) => {
    dispatch({ type: 'UPDATE_PROFILE', payload: profileData });
  };

  const clearError = () => {
    dispatch({ type: 'CLEAR_ERROR' });
  };

  const isAdmin = () => {
    return state.user?.role === 'admin';
  };

  const isCustomer = () => {
    return state.user?.role === 'customer';
  };

  const value = {
    user: state.user,
    isAuthenticated: state.isAuthenticated,
    error: state.error,
    loading: state.loading,
    login,
    register,
    logout,
    updateProfile,
    clearError,
    isAdmin,
    isCustomer
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
