import React, { createContext, useContext, useState, useEffect } from 'react';

const SettingsContext = createContext();

const defaultSettings = {
  // App Settings
  appName: 'YourShop',
  appDescription: 'Your destination for premium fashion and lifestyle products',
  appLogo: 'YS',
  
  // Contact Settings
  businessName: 'YourShop',
  email: '<EMAIL>',
  phone: '+****************',
  address: 'Your Address Here, Your City, State 12345',
  website: 'https://yourshop.com',
  
  // Theme Settings
  primaryColor: '#ec4899',
  secondaryColor: '#6b7280',
  
  // Notification Settings
  emailNotifications: true,
  orderNotifications: true,
  marketingEmails: false,
  
  // Security Settings
  twoFactorAuth: false,
  sessionTimeout: 30,
};

export const SettingsProvider = ({ children }) => {
  const [settings, setSettings] = useState(defaultSettings);

  useEffect(() => {
    // Load settings from localStorage on mount
    const savedSettings = localStorage.getItem('ecommerce_settings');
    if (savedSettings) {
      try {
        const parsedSettings = JSON.parse(savedSettings);
        setSettings(prev => ({ ...prev, ...parsedSettings }));
      } catch (error) {
        console.error('Error loading settings:', error);
      }
    }
  }, []);

  const updateSettings = (newSettings) => {
    const updatedSettings = { ...settings, ...newSettings };
    setSettings(updatedSettings);
    localStorage.setItem('ecommerce_settings', JSON.stringify(updatedSettings));
    return updatedSettings;
  };

  const updateSetting = (key, value) => {
    const updatedSettings = { ...settings, [key]: value };
    setSettings(updatedSettings);
    localStorage.setItem('ecommerce_settings', JSON.stringify(updatedSettings));
    return updatedSettings;
  };

  const resetSettings = () => {
    setSettings(defaultSettings);
    localStorage.setItem('ecommerce_settings', JSON.stringify(defaultSettings));
  };

  const getSetting = (key) => {
    return settings[key];
  };

  const value = {
    settings,
    updateSettings,
    updateSetting,
    resetSettings,
    getSetting
  };

  return (
    <SettingsContext.Provider value={value}>
      {children}
    </SettingsContext.Provider>
  );
};

export const useSettings = () => {
  const context = useContext(SettingsContext);
  if (!context) {
    throw new Error('useSettings must be used within a SettingsProvider');
  }
  return context;
};
